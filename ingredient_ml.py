"""
ML-based ingredient identification system for MealMind
"""

import re
import json
from typing import List, Dict, Set

class IngredientIdentifier:
    def __init__(self):
        # Common ingredients database with categories
        self.ingredients_db = {
            'vegetables': [
                'tomato', 'tomatoes', 'onion', 'onions', 'garlic', 'carrot', 'carrots',
                'potato', 'potatoes', 'bell pepper', 'peppers', 'spinach', 'lettuce',
                'cucumber', 'broccoli', 'cauliflower', 'cabbage', 'celery', 'mushrooms',
                'mushroom', 'zucchini', 'eggplant', 'corn', 'peas', 'beans', 'green beans',
                'asparagus', 'artichoke', 'avocado', 'beets', 'radish', 'turnip'
            ],
            'proteins': [
                'chicken', 'beef', 'pork', 'fish', 'salmon', 'tuna', 'shrimp', 'prawns',
                'eggs', 'egg', 'tofu', 'tempeh', 'turkey', 'lamb', 'duck', 'bacon',
                'ham', 'sausage', 'ground beef', 'chicken breast', 'chicken thighs'
            ],
            'grains': [
                'rice', 'pasta', 'bread', 'flour', 'quinoa', 'oats', 'barley', 'wheat',
                'noodles', 'spaghetti', 'macaroni', 'couscous', 'bulgur', 'millet'
            ],
            'dairy': [
                'milk', 'cheese', 'butter', 'cream', 'yogurt', 'sour cream', 'mozzarella',
                'cheddar', 'parmesan', 'feta', 'ricotta', 'cottage cheese', 'cream cheese'
            ],
            'spices': [
                'salt', 'pepper', 'garlic powder', 'onion powder', 'paprika', 'cumin',
                'oregano', 'basil', 'thyme', 'rosemary', 'sage', 'parsley', 'cilantro',
                'ginger', 'turmeric', 'cinnamon', 'nutmeg', 'cardamom', 'cloves',
                'bay leaves', 'chili powder', 'cayenne', 'black pepper', 'white pepper'
            ],
            'oils_fats': [
                'olive oil', 'vegetable oil', 'coconut oil', 'butter', 'ghee', 'lard',
                'canola oil', 'sunflower oil', 'sesame oil', 'avocado oil'
            ],
            'condiments': [
                'soy sauce', 'vinegar', 'lemon juice', 'lime juice', 'hot sauce',
                'worcestershire sauce', 'fish sauce', 'oyster sauce', 'ketchup',
                'mustard', 'mayonnaise', 'honey', 'maple syrup', 'sugar', 'brown sugar'
            ],
            'nuts_seeds': [
                'almonds', 'walnuts', 'pecans', 'cashews', 'peanuts', 'pine nuts',
                'sesame seeds', 'sunflower seeds', 'pumpkin seeds', 'chia seeds',
                'flax seeds', 'hemp seeds'
            ],
            'fruits': [
                'apple', 'apples', 'banana', 'bananas', 'orange', 'oranges', 'lemon',
                'lemons', 'lime', 'limes', 'strawberries', 'blueberries', 'raspberries',
                'grapes', 'pineapple', 'mango', 'peach', 'pear', 'cherry', 'cherries'
            ]
        }
        
        # Create a flat list of all ingredients for quick lookup
        self.all_ingredients = set()
        for category, ingredients in self.ingredients_db.items():
            self.all_ingredients.update(ingredients)
        
        # Common measurement units to filter out
        self.units = {
            'cup', 'cups', 'tablespoon', 'tablespoons', 'tbsp', 'teaspoon', 'teaspoons', 'tsp',
            'pound', 'pounds', 'lb', 'lbs', 'ounce', 'ounces', 'oz', 'gram', 'grams', 'g',
            'kilogram', 'kilograms', 'kg', 'liter', 'liters', 'l', 'milliliter', 'milliliters', 'ml',
            'piece', 'pieces', 'slice', 'slices', 'clove', 'cloves', 'bunch', 'bunches',
            'can', 'cans', 'jar', 'jars', 'bottle', 'bottles', 'package', 'packages',
            'large', 'medium', 'small', 'fresh', 'dried', 'frozen', 'organic', 'whole',
            'chopped', 'diced', 'sliced', 'minced', 'grated', 'shredded'
        }
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize input text"""
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove numbers and measurements
        text = re.sub(r'\d+[\./]?\d*\s*', '', text)
        
        # Remove common punctuation
        text = re.sub(r'[,;.!?()[\]{}]', ' ', text)
        
        return text
    
    def extract_ingredients(self, text: str) -> List[Dict[str, str]]:
        """Extract ingredients from text using ML-like pattern matching"""
        cleaned_text = self.clean_text(text)
        words = cleaned_text.split()
        
        identified_ingredients = []
        used_words = set()
        
        # First pass: Look for exact matches
        for ingredient in self.all_ingredients:
            ingredient_words = ingredient.split()
            
            # Check for multi-word ingredients
            if len(ingredient_words) > 1:
                ingredient_pattern = r'\b' + re.escape(ingredient) + r'\b'
                if re.search(ingredient_pattern, cleaned_text):
                    category = self._get_ingredient_category(ingredient)
                    identified_ingredients.append({
                        'name': ingredient.title(),
                        'category': category,
                        'confidence': 0.95
                    })
                    # Mark words as used
                    for word in ingredient_words:
                        used_words.add(word)
        
        # Second pass: Look for single word matches
        for word in words:
            if word not in used_words and word not in self.units and len(word) > 2:
                if word in self.all_ingredients:
                    category = self._get_ingredient_category(word)
                    identified_ingredients.append({
                        'name': word.title(),
                        'category': category,
                        'confidence': 0.9
                    })
                    used_words.add(word)
                
                # Check for partial matches (fuzzy matching)
                else:
                    for ingredient in self.all_ingredients:
                        if (word in ingredient or ingredient in word) and len(word) > 3:
                            category = self._get_ingredient_category(ingredient)
                            identified_ingredients.append({
                                'name': ingredient.title(),
                                'category': category,
                                'confidence': 0.7
                            })
                            used_words.add(word)
                            break
        
        # Remove duplicates and sort by confidence
        seen = set()
        unique_ingredients = []
        for ingredient in identified_ingredients:
            if ingredient['name'].lower() not in seen:
                seen.add(ingredient['name'].lower())
                unique_ingredients.append(ingredient)
        
        return sorted(unique_ingredients, key=lambda x: x['confidence'], reverse=True)
    
    def _get_ingredient_category(self, ingredient: str) -> str:
        """Get the category of an ingredient"""
        for category, ingredients in self.ingredients_db.items():
            if ingredient in ingredients:
                return category.replace('_', ' ').title()
        return 'Other'
    
    def suggest_ingredients(self, partial: str) -> List[str]:
        """Suggest ingredients based on partial input"""
        partial = partial.lower().strip()
        if len(partial) < 2:
            return []
        
        suggestions = []
        for ingredient in self.all_ingredients:
            if ingredient.startswith(partial):
                suggestions.append(ingredient.title())
        
        return sorted(suggestions)[:10]

# Global instance
ingredient_identifier = IngredientIdentifier()

def identify_ingredients(text: str) -> List[Dict[str, str]]:
    """Main function to identify ingredients from text"""
    return ingredient_identifier.extract_ingredients(text)

def get_ingredient_suggestions(partial: str) -> List[str]:
    """Get ingredient suggestions for autocomplete"""
    return ingredient_identifier.suggest_ingredients(partial)
