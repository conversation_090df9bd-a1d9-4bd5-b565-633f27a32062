import React, { useState, useEffect } from 'react';
import { <PERSON>, List, Sparkles, Refresh<PERSON><PERSON>, Brain } from 'lucide-react';
import { Ingredient, DetectedIngredient, Recipe } from './types';
import ImageUpload from './components/ImageUpload';
import IngredientInput from './components/IngredientInput';
import RecipeList from './components/RecipeList';
import { findMatchingRecipes } from './utils/recipeRecommendation';

type TabType = 'upload' | 'manual';

function App() {
  const [activeTab, setActiveTab] = useState<TabType>('upload');
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoadingRecipes, setIsLoadingRecipes] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleIngredientsDetected = (detectedIngredients: DetectedIngredient[]) => {
    const newIngredients: Ingredient[] = detectedIngredients.map(detected => ({
      id: detected.id,
      name: detected.name,
      category: detected.category,
      quantity: detected.quantity,
      unit: detected.unit
    }));
    setIngredients(newIngredients);
    setActiveTab('manual'); // Switch to manual tab to show detected ingredients
  };

  const searchRecipes = async () => {
    if (ingredients.length === 0) return;
    
    setIsLoadingRecipes(true);
    setHasSearched(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const matchingRecipes = findMatchingRecipes(ingredients);
    setRecipes(matchingRecipes);
    setIsLoadingRecipes(false);
  };

  const clearAll = () => {
    setIngredients([]);
    setRecipes([]);
    setHasSearched(false);
    setActiveTab('upload');
  };

  // Auto-search when ingredients are detected from image
  useEffect(() => {
    if (ingredients.length > 0 && activeTab === 'manual') {
      searchRecipes();
    }
  }, [ingredients, activeTab]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-emerald-500 rounded-xl">
                <Brain className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">MealMind</h1>
                <p className="text-sm text-gray-600">AI-Powered Recipe Generator</p>
              </div>
            </div>
            
            {ingredients.length > 0 && (
              <div className="flex items-center space-x-3">
                <button
                  onClick={searchRecipes}
                  disabled={isLoadingRecipes}
                  className="flex items-center space-x-2 px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 disabled:bg-emerald-300 transition-colors"
                >
                  {isLoadingRecipes ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Sparkles className="w-4 h-4" />
                  )}
                  <span>{isLoadingRecipes ? 'Finding Recipes...' : 'Find Recipes'}</span>
                </button>
                <button
                  onClick={clearAll}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Clear All
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        {!hasSearched && (
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Turn Your Ingredients Into <span className="text-emerald-500">Amazing Meals</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Upload a photo of your fridge or manually enter your ingredients, and our AI will suggest 
              personalized recipes you can make right now.
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Input Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-sm border p-6 sticky top-24">
              {/* Tab Navigation */}
              <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setActiveTab('upload')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all ${
                    activeTab === 'upload'
                      ? 'bg-white text-emerald-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <Camera className="w-4 h-4" />
                  <span>Photo</span>
                </button>
                <button
                  onClick={() => setActiveTab('manual')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all ${
                    activeTab === 'manual'
                      ? 'bg-white text-emerald-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <List className="w-4 h-4" />
                  <span>Manual</span>
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === 'upload' ? (
                <ImageUpload onIngredientsDetected={handleIngredientsDetected} />
              ) : (
                <IngredientInput
                  ingredients={ingredients}
                  onIngredientsChange={setIngredients}
                />
              )}
            </div>
          </div>

          {/* Results Section */}
          <div className="lg:col-span-2">
            {hasSearched ? (
              <RecipeList
                recipes={recipes}
                availableIngredients={ingredients}
                isLoading={isLoadingRecipes}
              />
            ) : (
              <div className="bg-white rounded-2xl shadow-sm border p-12 text-center">
                <div className="max-w-md mx-auto">
                  <div className="w-24 h-24 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Brain className="w-12 h-12 text-emerald-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">
                    Ready to Discover Amazing Recipes?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Upload a photo of your fridge or add ingredients manually to get started. 
                    Our AI will analyze what you have and suggest delicious recipes you can make.
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="p-3 bg-emerald-50 rounded-lg">
                      <Camera className="w-6 h-6 text-emerald-500 mx-auto mb-2" />
                      <p className="font-medium text-emerald-800">Smart Detection</p>
                      <p className="text-emerald-600">AI identifies ingredients from photos</p>
                    </div>
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <Sparkles className="w-6 h-6 text-blue-500 mx-auto mb-2" />
                      <p className="font-medium text-blue-800">Perfect Matches</p>
                      <p className="text-blue-600">Recipes tailored to your ingredients</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-gray-600">
              Built with ❤️ using AI technology to reduce food waste and inspire cooking
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;