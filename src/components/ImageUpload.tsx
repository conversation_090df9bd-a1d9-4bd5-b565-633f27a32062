import React, { useCallback, useState } from 'react';
import { Upload, Camera, X, Loader } from 'lucide-react';
import { DetectedIngredient } from '../types';
import { detectIngredientsFromImage, validateImageFile } from '../utils/imageProcessing';

interface ImageUploadProps {
  onIngredientsDetected: (ingredients: DetectedIngredient[]) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onIngredientsDetected }) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    if (!validateImageFile(file)) {
      alert('Please upload a valid image file (JPEG, PNG, WebP) under 10MB.');
      return;
    }

    const imageUrl = URL.createObjectURL(file);
    setUploadedImage(imageUrl);
    setIsProcessing(true);
    setProcessingProgress(0);

    // Simulate processing progress
    const progressInterval = setInterval(() => {
      setProcessingProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 15;
      });
    }, 200);

    try {
      const detectedIngredients = await detectIngredientsFromImage(file);
      setProcessingProgress(100);
      setTimeout(() => {
        onIngredientsDetected(detectedIngredients);
        setIsProcessing(false);
      }, 500);
    } catch (error) {
      console.error('Error processing image:', error);
      setIsProcessing(false);
      alert('Error processing image. Please try again.');
    }
  };

  const clearImage = () => {
    setUploadedImage(null);
    setIsProcessing(false);
    setProcessingProgress(0);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <Camera className="mx-auto h-12 w-12 text-emerald-500 mb-4" />
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Upload Your Fridge Photo</h3>
        <p className="text-gray-600">Our AI will identify ingredients and suggest recipes</p>
      </div>

      {!uploadedImage ? (
        <div
          className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
            dragActive
              ? 'border-emerald-400 bg-emerald-50'
              : 'border-gray-300 hover:border-emerald-400 hover:bg-gray-50'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-700 mb-2">
            Drag and drop your fridge photo here
          </p>
          <p className="text-gray-500 mb-4">or</p>
          <label className="inline-flex items-center px-6 py-3 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors cursor-pointer">
            <Upload className="w-5 h-5 mr-2" />
            Choose File
            <input
              type="file"
              className="hidden"
              accept="image/*"
              onChange={handleFileInput}
            />
          </label>
          <p className="text-sm text-gray-400 mt-4">
            Supports JPEG, PNG, WebP up to 10MB
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="relative">
            <img
              src={uploadedImage}
              alt="Uploaded fridge"
              className="w-full h-64 object-cover rounded-xl shadow-lg"
            />
            <button
              onClick={clearImage}
              className="absolute top-2 right-2 p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {isProcessing && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <div className="flex items-center space-x-3 mb-4">
                <Loader className="w-5 h-5 text-emerald-500 animate-spin" />
                <span className="font-medium text-gray-700">Analyzing your fridge...</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-emerald-500 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${processingProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {processingProgress < 30 && 'Detecting objects...'}
                {processingProgress >= 30 && processingProgress < 70 && 'Identifying ingredients...'}
                {processingProgress >= 70 && processingProgress < 100 && 'Finalizing results...'}
                {processingProgress === 100 && 'Complete!'}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ImageUpload;