import React, { useState, useRef } from 'react';
import { Plus, X, ShoppingCart } from 'lucide-react';
import { Ingredient } from '../types';
import { commonIngredients, ingredientCategories } from '../data/recipes';

interface IngredientInputProps {
  ingredients: Ingredient[];
  onIngredientsChange: (ingredients: Ingredient[]) => void;
}

const IngredientInput: React.FC<IngredientInputProps> = ({ ingredients, onIngredientsChange }) => {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (value: string) => {
    setInputValue(value);
    
    if (value.length > 0) {
      const filtered = commonIngredients.filter(ingredient =>
        ingredient.toLowerCase().includes(value.toLowerCase()) &&
        !ingredients.some(ing => ing.name.toLowerCase() === ingredient.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 6));
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const addIngredient = (name: string) => {
    if (name.trim() && !ingredients.some(ing => ing.name.toLowerCase() === name.toLowerCase())) {
      const newIngredient: Ingredient = {
        id: Date.now().toString(),
        name: name.trim(),
        category: getCategoryForIngredient(name.trim())
      };
      onIngredientsChange([...ingredients, newIngredient]);
      setInputValue('');
      setShowSuggestions(false);
      inputRef.current?.focus();
    }
  };

  const removeIngredient = (id: string) => {
    onIngredientsChange(ingredients.filter(ing => ing.id !== id));
  };

  const getCategoryForIngredient = (name: string): string => {
    const lowerName = name.toLowerCase();
    if (['chicken', 'beef', 'fish', 'eggs', 'bacon'].some(item => lowerName.includes(item))) {
      return 'Proteins';
    }
    if (['cheese', 'milk', 'butter', 'yogurt'].some(item => lowerName.includes(item))) {
      return 'Dairy';
    }
    if (['tomato', 'onion', 'pepper', 'carrot', 'lettuce', 'broccoli'].some(item => lowerName.includes(item))) {
      return 'Vegetables';
    }
    if (['pasta', 'rice', 'bread', 'flour'].some(item => lowerName.includes(item))) {
      return 'Grains';
    }
    return 'Other';
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (suggestions.length > 0 && showSuggestions) {
        addIngredient(suggestions[0]);
      } else {
        addIngredient(inputValue);
      }
    }
  };

  const groupedIngredients = ingredients.reduce((groups, ingredient) => {
    const category = ingredient.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(ingredient);
    return groups;
  }, {} as Record<string, Ingredient[]>);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <ShoppingCart className="mx-auto h-12 w-12 text-emerald-500 mb-4" />
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Enter Your Ingredients</h3>
        <p className="text-gray-600">Type ingredients you have available</p>
      </div>

      <div className="relative">
        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type an ingredient (e.g., chicken, tomatoes, cheese)"
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          />
          <button
            onClick={() => addIngredient(inputValue)}
            disabled={!inputValue.trim()}
            className="px-6 py-3 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>

        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => addIngredient(suggestion)}
                className="w-full px-4 py-2 text-left hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {ingredients.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-800">Your Ingredients ({ingredients.length})</h4>
          
          {Object.entries(groupedIngredients).map(([category, categoryIngredients]) => (
            <div key={category} className="space-y-2">
              <h5 className="text-sm font-medium text-gray-600 uppercase tracking-wide">
                {category}
              </h5>
              <div className="flex flex-wrap gap-2">
                {categoryIngredients.map((ingredient) => (
                  <span
                    key={ingredient.id}
                    className="inline-flex items-center px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-sm"
                  >
                    {ingredient.name}
                    {ingredient.quantity && ` (${ingredient.quantity}${ingredient.unit ? ' ' + ingredient.unit : ''})`}
                    <button
                      onClick={() => removeIngredient(ingredient.id)}
                      className="ml-2 text-emerald-600 hover:text-emerald-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {ingredients.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No ingredients added yet. Start typing to add some!</p>
          <div className="mt-4">
            <p className="text-sm text-gray-400 mb-2">Popular ingredients:</p>
            <div className="flex flex-wrap justify-center gap-2">
              {commonIngredients.slice(0, 8).map((ingredient) => (
                <button
                  key={ingredient}
                  onClick={() => addIngredient(ingredient)}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-emerald-100 hover:text-emerald-800 transition-colors"
                >
                  {ingredient}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IngredientInput;