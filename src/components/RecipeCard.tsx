import React from 'react';
import { Clock, Users, ChefHat, CheckCircle, AlertCircle, Heart } from 'lucide-react';
import { Recipe, Ingredient } from '../types';
import { calculateMissingIngredients } from '../utils/recipeRecommendation';

interface RecipeCardProps {
  recipe: Recipe;
  availableIngredients: Ingredient[];
  onViewRecipe: (recipe: Recipe) => void;
  isFavorite: boolean;
  onToggleFavorite: (recipeId: string) => void;
}

const RecipeCard: React.FC<RecipeCardProps> = ({ 
  recipe, 
  availableIngredients, 
  onViewRecipe, 
  isFavorite, 
  onToggleFavorite 
}) => {
  const missingIngredients = calculateMissingIngredients(recipe, availableIngredients);
  const canMake = missingIngredients.length === 0;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-600 bg-green-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'Hard': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDietaryTypeColor = (type: string) => {
    switch (type) {
      case 'vegan': return 'text-green-600 bg-green-100';
      case 'vegetarian': return 'text-blue-600 bg-blue-100';
      case 'non-vegetarian': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border hover:shadow-md transition-all duration-200 overflow-hidden group">
      <div className="relative">
        <img
          src={recipe.image}
          alt={recipe.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
        />
        <div className="absolute top-3 right-3 flex space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite(recipe.id);
            }}
            className={`p-2 rounded-full shadow-lg transition-colors ${
              isFavorite 
                ? 'bg-red-500 text-white hover:bg-red-600' 
                : 'bg-white text-gray-600 hover:bg-gray-100'
            }`}
          >
            <Heart className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
          </button>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
            canMake ? 'bg-green-500 text-white' : 'bg-orange-500 text-white'
          }`}>
            {canMake ? (
              <>
                <CheckCircle className="w-3 h-3" />
                <span>Can Make</span>
              </>
            ) : (
              <>
                <AlertCircle className="w-3 h-3" />
                <span>{missingIngredients.length} Missing</span>
              </>
            )}
          </div>
        </div>
        {recipe.matchScore && (
          <div className="absolute top-3 left-3 bg-emerald-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            {recipe.matchScore}% Match
          </div>
        )}
      </div>

      <div className="p-5">
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-800 group-hover:text-emerald-600 transition-colors">
            {recipe.title}
          </h3>
          <div className="flex space-x-1">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(recipe.difficulty)}`}>
              {recipe.difficulty}
            </span>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {recipe.description}
        </p>

        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{recipe.prepTime + recipe.cookTime} min</span>
          </div>
          <div className="flex items-center space-x-1">
            <Users className="w-4 h-4" />
            <span>{recipe.servings} servings</span>
          </div>
          <div className="flex items-center space-x-1">
            <ChefHat className="w-4 h-4" />
            <span>{recipe.difficulty}</span>
          </div>
        </div>

        <div className="mb-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDietaryTypeColor(recipe.dietaryType)}`}>
            {recipe.dietaryType.charAt(0).toUpperCase() + recipe.dietaryType.slice(1)}
          </span>
          <span className="ml-2 px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">
            {recipe.cuisine}
          </span>
        </div>

        {missingIngredients.length > 0 && (
          <div className="mb-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Missing ingredients:</p>
            <div className="flex flex-wrap gap-1">
              {missingIngredients.slice(0, 3).map((ingredient) => (
                <span
                  key={ingredient}
                  className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full"
                >
                  {ingredient}
                </span>
              ))}
              {missingIngredients.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  +{missingIngredients.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="flex flex-wrap gap-1 mb-4">
          {recipe.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
            >
              #{tag}
            </span>
          ))}
        </div>

        <button 
          onClick={() => onViewRecipe(recipe)}
          className="w-full bg-emerald-500 text-white py-2 rounded-lg hover:bg-emerald-600 transition-colors font-medium"
        >
          View Recipe
        </button>
      </div>
    </div>
  );
};

export default RecipeCard;