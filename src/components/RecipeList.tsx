import React, { useState } from 'react';
import { ChefHat, <PERSON>rk<PERSON>, Heart } from 'lucide-react';
import { Recipe, Ingredient, RecipeFilters } from '../types';
import RecipeCard from './RecipeCard';
import RecipeFiltersComponent from './RecipeFilters';
import RecipeModal from './RecipeModal';

interface RecipeListProps {
  recipes: Recipe[];
  availableIngredients: Ingredient[];
  isLoading?: boolean;
}

const RecipeList: React.FC<RecipeListProps> = ({ recipes, availableIngredients, isLoading }) => {
  const [filters, setFilters] = useState<RecipeFilters>({
    dietaryType: 'all',
    maxPrepTime: 999,
    difficulty: 'All',
    cuisine: 'All'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  const toggleFavorite = (recipeId: string) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(recipeId)) {
      newFavorites.delete(recipeId);
    } else {
      newFavorites.add(recipeId);
    }
    setFavorites(newFavorites);
  };

  const handleViewRecipe = (recipe: Recipe) => {
    setSelectedRecipe(recipe);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedRecipe(null);
  };

  const applyFilters = (recipesToFilter: Recipe[]) => {
    return recipesToFilter.filter(recipe => {
      if (filters.dietaryType !== 'all' && recipe.dietaryType !== filters.dietaryType) {
        return false;
      }
      if (recipe.prepTime > filters.maxPrepTime) {
        return false;
      }
      if (filters.difficulty !== 'All' && recipe.difficulty !== filters.difficulty) {
        return false;
      }
      if (filters.cuisine !== 'All' && recipe.cuisine !== filters.cuisine) {
        return false;
      }
      if (showFavoritesOnly && !favorites.has(recipe.id)) {
        return false;
      }
      return true;
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <ChefHat className="mx-auto h-12 w-12 text-emerald-500 animate-pulse mb-4" />
          <h3 className="text-xl font-semibold text-gray-800 mb-2">Finding Perfect Recipes...</h3>
          <p className="text-gray-600">Analyzing your ingredients to suggest the best matches</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border overflow-hidden">
              <div className="w-full h-48 bg-gray-200 animate-pulse"></div>
              <div className="p-5 space-y-3">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (recipes.length === 0) {
    return (
      <div className="text-center py-12">
        <ChefHat className="mx-auto h-16 w-16 text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-800 mb-2">No Recipes Found</h3>
        <p className="text-gray-600 mb-6">
          We couldn't find recipes matching your ingredients. Try adding more ingredients or different combinations.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
          <p className="text-sm text-blue-800">
            <strong>Tip:</strong> Try common ingredients like eggs, milk, onions, or garlic to get more recipe suggestions.
          </p>
        </div>
      </div>
    );
  }

  const filteredRecipes = applyFilters(recipes);

  const canMakeRecipes = filteredRecipes.filter(recipe => {
    const missingIngredients = recipe.ingredients.filter(ingredient => 
      !availableIngredients.some(available => 
        available.name.toLowerCase().includes(ingredient.toLowerCase()) || 
        ingredient.toLowerCase().includes(available.name.toLowerCase())
      )
    );
    return missingIngredients.length === 0;
  });

  const almostCanMakeRecipes = filteredRecipes.filter(recipe => {
    const missingIngredients = recipe.ingredients.filter(ingredient => 
      !availableIngredients.some(available => 
        available.name.toLowerCase().includes(ingredient.toLowerCase()) || 
        ingredient.toLowerCase().includes(available.name.toLowerCase())
      )
    );
    return missingIngredients.length > 0 && missingIngredients.length <= 2;
  });

  const otherRecipes = filteredRecipes.filter(recipe => {
    const missingIngredients = recipe.ingredients.filter(ingredient => 
      !availableIngredients.some(available => 
        available.name.toLowerCase().includes(ingredient.toLowerCase()) || 
        ingredient.toLowerCase().includes(available.name.toLowerCase())
      )
    );
    return missingIngredients.length > 2;
  });

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="flex items-center justify-center space-x-2 mb-4">
          <Sparkles className="h-8 w-8 text-emerald-500" />
          <h3 className="text-2xl font-bold text-gray-800">Recipe Recommendations</h3>
        </div>
        <p className="text-gray-600">Found {filteredRecipes.length} recipes based on your ingredients and filters</p>
      </div>

      <div className="flex items-center justify-between">
        <RecipeFiltersComponent
          filters={filters}
          onFiltersChange={setFilters}
          isVisible={showFilters}
          onToggle={() => setShowFilters(!showFilters)}
        />
        
        <button
          onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            showFavoritesOnly 
              ? 'bg-red-500 text-white hover:bg-red-600' 
              : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <Heart className={`w-4 h-4 ${showFavoritesOnly ? 'fill-current' : ''}`} />
          <span>{showFavoritesOnly ? 'Show All' : 'Favorites Only'}</span>
          {favorites.size > 0 && (
            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
              {favorites.size}
            </span>
          )}
        </button>
      </div>

      {filteredRecipes.length === 0 ? (
        <div className="text-center py-12">
          <ChefHat className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-800 mb-2">No Recipes Match Your Filters</h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your filters or adding more ingredients to see more recipes.
          </p>
        </div>
      ) : (
        <>
          {canMakeRecipes.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-green-600 flex items-center space-x-2">
                <ChefHat className="w-5 h-5" />
                <span>Ready to Cook ({canMakeRecipes.length})</span>
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {canMakeRecipes.map((recipe) => (
                  <RecipeCard
                    key={recipe.id}
                    recipe={recipe}
                    availableIngredients={availableIngredients}
                    onViewRecipe={handleViewRecipe}
                    isFavorite={favorites.has(recipe.id)}
                    onToggleFavorite={toggleFavorite}
                  />
                ))}
              </div>
            </div>
          )}

          {almostCanMakeRecipes.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-orange-600 flex items-center space-x-2">
                <ChefHat className="w-5 h-5" />
                <span>Almost Ready ({almostCanMakeRecipes.length})</span>
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {almostCanMakeRecipes.map((recipe) => (
                  <RecipeCard
                    key={recipe.id}
                    recipe={recipe}
                    availableIngredients={availableIngredients}
                    onViewRecipe={handleViewRecipe}
                    isFavorite={favorites.has(recipe.id)}
                    onToggleFavorite={toggleFavorite}
                  />
                ))}
              </div>
            </div>
          )}

          {otherRecipes.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-gray-600 flex items-center space-x-2">
                <ChefHat className="w-5 h-5" />
                <span>More Ideas ({otherRecipes.length})</span>
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {otherRecipes.map((recipe) => (
                  <RecipeCard
                    key={recipe.id}
                    recipe={recipe}
                    availableIngredients={availableIngredients}
                    onViewRecipe={handleViewRecipe}
                    isFavorite={favorites.has(recipe.id)}
                    onToggleFavorite={toggleFavorite}
                  />
                ))}
              </div>
            </div>
          )}
        </>
      )}

      <RecipeModal
        recipe={selectedRecipe}
        isOpen={showModal}
        onClose={handleCloseModal}
        isFavorite={selectedRecipe ? favorites.has(selectedRecipe.id) : false}
        onToggleFavorite={() => selectedRecipe && toggleFavorite(selectedRecipe.id)}
      />
    </div>
  );
};

export default RecipeList;