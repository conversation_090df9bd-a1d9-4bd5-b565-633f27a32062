export interface Ingredient {
  id: string;
  name: string;
  category: string;
  quantity?: string;
  unit?: string;
}

export interface Recipe {
  id: string;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  prepTime: number;
  cookTime: number;
  servings: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  image: string;
  tags: string[];
  dietaryType: 'vegetarian' | 'vegan' | 'non-vegetarian';
  cuisine: string;
  matchScore?: number;
}

export interface DetectedIngredient extends Ingredient {
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface RecipeFilters {
  dietaryType: string;
  maxPrepTime: number;
  difficulty: string;
  cuisine: string;
}