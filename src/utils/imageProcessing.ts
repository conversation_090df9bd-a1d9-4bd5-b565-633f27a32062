import { DetectedIngredient } from '../types';
import { commonIngredients } from '../data/recipes';

// Simulate ML ingredient detection from image
export const detectIngredientsFromImage = async (imageFile: File): Promise<DetectedIngredient[]> => {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));
  
  // Mock detected ingredients with confidence scores
  const simulatedDetections: DetectedIngredient[] = [
    {
      id: '1',
      name: 'eggs',
      category: 'Dairy',
      confidence: 0.95,
      quantity: '6',
      unit: 'pieces'
    },
    {
      id: '2',
      name: 'milk',
      category: 'Dairy',
      confidence: 0.88,
      quantity: '1',
      unit: 'bottle'
    },
    {
      id: '3',
      name: 'tomatoes',
      category: 'Vegetables',
      confidence: 0.92,
      quantity: '4',
      unit: 'pieces'
    },
    {
      id: '4',
      name: 'bell pepper',
      category: 'Vegetables',
      confidence: 0.85,
      quantity: '2',
      unit: 'pieces'
    },
    {
      id: '5',
      name: 'carrot',
      category: 'Vegetables',
      confidence: 0.78,
      quantity: '3',
      unit: 'pieces'
    },
    {
      id: '6',
      name: 'cheese',
      category: 'Dairy',
      confidence: 0.91,
      quantity: '200g',
      unit: 'block'
    },
    {
      id: '7',
      name: 'onion',
      category: 'Vegetables',
      confidence: 0.82,
      quantity: '2',
      unit: 'pieces'
    },
    {
      id: '8',
      name: 'lettuce',
      category: 'Vegetables',
      confidence: 0.75,
      quantity: '1',
      unit: 'head'
    }
  ];

  // Randomly select 4-8 ingredients to simulate detection variability
  const numDetections = Math.floor(Math.random() * 5) + 4;
  return simulatedDetections.slice(0, numDetections);
};

export const validateImageFile = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
};