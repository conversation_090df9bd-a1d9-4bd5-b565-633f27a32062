import { Recipe, Ingredient } from '../types';
import { mockRecipes } from '../data/recipes';

export const findMatchingRecipes = (availableIngredients: Ingredient[]): Recipe[] => {
  const ingredientNames = availableIngredients.map(ing => ing.name.toLowerCase());
  
  const recipesWithScores = mockRecipes.map(recipe => {
    const recipeIngredients = recipe.ingredients.map(ing => ing.toLowerCase());
    const matchingIngredients = recipeIngredients.filter(ing => 
      ingredientNames.some(available => available.includes(ing) || ing.includes(available))
    );
    
    const matchScore = (matchingIngredients.length / recipeIngredients.length) * 100;
    
    return {
      ...recipe,
      matchScore: Math.round(matchScore)
    };
  });
  
  // Filter recipes with at least 30% match and sort by match score
  return recipesWithScores
    .filter(recipe => recipe.matchScore! >= 30)
    .sort((a, b) => b.matchScore! - a.matchScore!);
};

export const calculateMissingIngredients = (recipe: Recipe, availableIngredients: Ingredient[]): string[] => {
  const availableNames = availableIngredients.map(ing => ing.name.toLowerCase());
  
  return recipe.ingredients.filter(ingredient => 
    !availableNames.some(available => 
      available.includes(ingredient.toLowerCase()) || ingredient.toLowerCase().includes(available)
    )
  );
};