/* Organic Design Overrides for MealMind */

/* Hide old floating elements */
.bg-elements,
.floating-shape {
    display: none !important;
}

/* Override any remaining old styles */
.modern-header,
.nav-container,
.nav-brand,
.nav-menu,
.hero-section,
.hero-content,
.ingredient-finder-section,
.method-card,
.quick-actions-section,
.features-section {
    all: unset !important;
    display: block !important;
}

/* Ensure Tailwind styles take precedence */
* {
    box-sizing: border-box;
}

/* Custom animations for organic feel */
@keyframes gentle-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.animate-gentle-bounce {
    animation: gentle-bounce 2s infinite;
}

/* Smooth transitions for organic feel */
.transition-organic {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom focus styles */
.focus-organic:focus {
    outline: none;
    ring: 2px;
    ring-color: #1A5E2A;
    ring-opacity: 0.3;
    border-color: #1A5E2A;
}

/* Recipe card hover effects */
.recipe-card-organic {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recipe-card-organic:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(26, 94, 42, 0.15);
}

/* Ingredient tag styles */
.ingredient-tag {
    background: #E8F5E8;
    color: #1A5E2A;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
    margin: 0.125rem;
}

/* Custom scrollbar for organic feel */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #F9F5ED;
}

::-webkit-scrollbar-thumb {
    background: #1A5E2A;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2D7A3D;
}

/* Notification styles */
.notification {
    background: white;
    border: 1px solid #E8F5E8;
    border-left: 4px solid #1A5E2A;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading states */
.loading-organic {
    background: linear-gradient(90deg, #F9F5ED 25%, #E8F5E8 50%, #F9F5ED 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
}

/* Print styles for recipes */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .print-friendly {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}
