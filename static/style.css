/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #1a202c;
    background: #fafafa;
    min-height: 100vh;
    overflow-x: hidden;
    font-weight: 400;
}

/* Responsive Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 3rem;
    }
}

/* Floating Background Elements */
.bg-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.15), rgba(139, 69, 19, 0.1));
    animation: float 20s infinite linear;
    backdrop-filter: blur(2px);
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 15s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(90deg); }
    50% { transform: translateY(-40px) rotate(180deg); }
    75% { transform: translateY(-20px) rotate(270deg); }
}

/* Modern Header and Navigation */
.modern-header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0,0,0,0.04);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@media (min-width: 768px) {
    .nav-container {
        padding: 1rem 2rem;
    }
}

@media (min-width: 1024px) {
    .nav-container {
        max-width: 1400px;
        padding: 1rem 3rem;
    }
}

.nav-brand {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.nav-brand h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d5016;
    margin: 0;
    line-height: 1;
    font-family: 'Inter', sans-serif;
    letter-spacing: -0.02em;
}

.brand-tagline {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    margin-top: 0.2rem;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

@media (min-width: 768px) {
    .nav-brand h1 {
        font-size: 2.2rem;
    }

    .brand-tagline {
        font-size: 0.8rem;
        letter-spacing: 1px;
    }
}

@media (min-width: 1024px) {
    .nav-brand h1 {
        font-size: 2.8rem;
    }

    .brand-tagline {
        font-size: 0.9rem;
    }
}

.nav-menu {
    display: none;
    list-style: none;
    gap: 2rem;
    align-items: center;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    flex-direction: column;
    padding: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(20px);
}

.nav-menu.active {
    display: flex;
}

.nav-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.95rem;
    font-family: 'Inter', sans-serif;
    letter-spacing: -0.01em;
    width: 100%;
    text-align: center;
}

@media (min-width: 768px) {
    .nav-menu {
        display: flex;
        position: static;
        background: none;
        flex-direction: row;
        padding: 0;
        border-radius: 0;
        box-shadow: none;
        gap: 1rem;
        backdrop-filter: none;
    }

    .nav-link {
        font-size: 0.95rem;
        padding: 0.75rem 1.25rem;
        width: auto;
    }
}

.nav-link:hover {
    background: #f3f4f6;
    color: #2d5016;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
}

.nav-link.active {
    background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
    color: #2c1810;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
    font-weight: 600;
}

.nav-cta {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%) !important;
    color: #f4e4bc !important;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.4);
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.nav-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.6);
    background: linear-gradient(135deg, #a0522d 0%, #8b4513 100%) !important;
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #f4e4bc;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(212, 175, 55, 0.2);
    color: #d4af37;
}

@media (min-width: 768px) {
    .mobile-menu-toggle {
        display: none;
    }
}

/* Modern Main Content */
.modern-main, .browse-main, .favorites-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

@media (min-width: 768px) {
    .modern-main, .browse-main, .favorites-main {
        padding: 2rem;
    }
}

/* Hero Section */
.hero-section {
    padding: 4rem 0 5rem;
    background: #ffffff;
    margin: 0;
    position: relative;
}

@media (min-width: 768px) {
    .hero-section {
        padding: 6rem 0 7rem;
    }
}

@media (min-width: 1024px) {
    .hero-section {
        padding: 8rem 0 9rem;
    }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-text {
    text-align: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    font-family: 'Inter', sans-serif;
    color: #1f2937;
    letter-spacing: -0.02em;
}

@media (min-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        padding: 0 2rem;
    }

    .hero-text {
        text-align: left;
    }

    .hero-title {
        font-size: 4.5rem;
        margin-bottom: 2rem;
    }
}

@media (min-width: 1024px) {
    .hero-content {
        gap: 4rem;
        padding: 0 3rem;
    }

    .hero-title {
        font-size: 5.5rem;
    }
}

.title-line {
    display: block;
    margin-bottom: 0.5rem;
}

.gradient-text {
    background: linear-gradient(135deg, #2d5016 0%, #4ade80 50%, #16a34a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #6b7280;
    font-weight: 400;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-family: 'Inter', sans-serif;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: center;
}

@media (min-width: 768px) {
    .hero-subtitle {
        font-size: 1.3rem;
        margin-bottom: 2.5rem;
    }

    .hero-cta {
        justify-content: flex-start;
    }

    .hero-stats {
        gap: 1.5rem;
        justify-content: flex-start;
    }
}

@media (min-width: 1024px) {
    .hero-subtitle {
        font-size: 1.4rem;
        margin-bottom: 3rem;
    }

    .hero-stats {
        gap: 2rem;
        margin-top: 2rem;
    }
}

.hero-stats .stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    min-width: 80px;
}

.hero-stats .stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 800;
    color: #667eea;
}

.hero-stats .stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 600;
}

.hero-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.ingredient-showcase {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.ingredient-item {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    animation: bounce 2s infinite;
    animation-delay: var(--delay);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.magic-arrow {
    font-size: 2rem;
    color: #667eea;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Search section */
.search-section {
    display: flex;
    justify-content: center;
    margin-bottom: 4rem;
    gap: 1rem;
}

.search-section input {
    padding: 1rem 1.5rem;
    border: 3px solid transparent;
    border-radius: 50px;
    font-size: 1.1rem;
    width: 400px;
    outline: none;
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.search-section input:focus {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
}

/* Buttons */
.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: #2d5016;
    color: white;
    box-shadow: 0 4px 14px rgba(45, 80, 22, 0.25);
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    border: none;
    letter-spacing: -0.01em;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.35);
    background: #1f3a10;
}

.btn-secondary {
    background: transparent;
    color: #2d5016;
    box-shadow: none;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    border: 2px solid #2d5016;
    letter-spacing: -0.01em;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    background: #2d5016;
    color: white;
    box-shadow: 0 4px 14px rgba(45, 80, 22, 0.25);
}

.btn-small {
    padding: 0.7rem 1.5rem;
    font-size: 0.95rem;
}

/* Recipe Grid */
.recipes-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .recipes-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .recipes-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
        padding: 0;
    }
}

@media (min-width: 1400px) {
    .recipes-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.recipe-card, .favorite-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    max-width: 400px;
    margin: 0 auto;
}

.recipe-card:before, .favorite-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #2d5016;
}

.recipe-card:hover, .favorite-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    border-color: rgba(45, 80, 22, 0.1);
}

@media (min-width: 768px) {
    .recipe-card, .favorite-card {
        padding: 2rem;
        border-radius: 25px;
        max-width: none;
        margin: 0;
    }
}

.recipe-card h3, .favorite-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    text-transform: capitalize;
    letter-spacing: -0.01em;
}

@media (min-width: 768px) {
    .recipe-card h3, .favorite-card h3 {
        font-size: 1.6rem;
        margin-bottom: 1.2rem;
    }
}

@media (min-width: 1024px) {
    .recipe-card h3, .favorite-card h3 {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }
}

.recipe-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.recipe-info span {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.ingredients {
    margin-bottom: 2rem;
}

.ingredients strong {
    color: #2d3748;
    display: block;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 700;
}

.ingredients ul {
    list-style: none;
    padding-left: 0;
}

.ingredients li {
    padding: 0.5rem 0;
    color: #4a5568;
    position: relative;
    padding-left: 2rem;
    font-weight: 500;
}

.ingredients li:before {
    content: "🌿";
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

.recipe-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Recipe Detail Page */
.recipe-detail {
    max-width: 900px;
    margin: 0 auto;
}

.recipe-header {
    text-align: center;
    margin-bottom: 4rem;
    padding: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 30px;
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.recipe-header h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
}

.recipe-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.recipe-meta span {
    background: rgba(255,255,255,0.2);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    backdrop-filter: blur(10px);
}

.recipe-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 4rem;
}

.ingredients-section,
.instructions-section {
    background: linear-gradient(135deg, rgba(244, 228, 188, 0.9), rgba(255, 255, 255, 0.95));
    padding: 2.5rem;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(139, 69, 19, 0.15);
    border: 2px solid rgba(212, 175, 55, 0.2);
    backdrop-filter: blur(10px);
}

.ingredients-section h2,
.instructions-section h2 {
    color: #2c1810;
    margin-bottom: 2rem;
    font-size: 2.2rem;
    font-weight: 700;
    font-family: 'Playfair Display', serif;
    background: linear-gradient(135deg, #d4af37 0%, #8b4513 50%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Ingredient Categories */
.ingredient-category {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 15px;
    border-left: 4px solid #d4af37;
}

.category-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 1rem;
    font-family: 'Lato', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.ingredients-list {
    list-style: none;
    padding: 0;
}

.ingredients-list li {
    padding: 1rem 0;
    border-bottom: 1px solid #e2e8f0;
    position: relative;
    padding-left: 2.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    color: #4a5568;
}

.ingredients-list li:before, .ingredients-preview ul li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #d4af37;
    font-size: 1.2rem;
    font-weight: bold;
}

/* Enhanced Instructions */
.instructions-list {
    list-style: none;
    padding: 0;
    counter-reset: step-counter;
}

.instruction-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border-left: 4px solid #d4af37;
    counter-increment: step-counter;
    transition: all 0.3s ease;
}

.instruction-step:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateX(5px);
}

.step-number {
    background: linear-gradient(135deg, #d4af37 0%, #8b4513 100%);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.step-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #2c1810;
    font-family: 'Lato', sans-serif;
}

.cooking-tips-enhanced {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(139, 69, 19, 0.05));
    border-radius: 15px;
    border: 2px solid rgba(212, 175, 55, 0.3);
}

.cooking-tips-enhanced h3 {
    color: #8b4513;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
    font-family: 'Playfair Display', serif;
}

.cooking-tips-enhanced p {
    color: #5d4037;
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.6;
}

.instructions {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #4a5568;
    font-weight: 500;
}

.nutrition-section {
    grid-column: 1 / -1;
    background: white;
    padding: 2.5rem;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.nutrition-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.nutrition-item {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(245, 87, 108, 0.3);
    transition: transform 0.3s ease;
}

.nutrition-item:hover {
    transform: translateY(-5px);
}

.nutrition-label {
    display: block;
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 600;
}

.nutrition-value {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    margin-top: 0.5rem;
}

/* Meal Plan Styles */
.meal-plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.meal-plan-header h1 {
    font-size: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.meal-plan-controls {
    display: flex;
    gap: 1rem;
}

.current-meals h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2d3748;
    font-weight: 700;
}

.meals-list {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.meal-item {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 5px solid #667eea;
    transition: all 0.3s ease;
}

.meal-item:hover {
    transform: translateX(10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.meal-info h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.meal-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.meal-meta span {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
}

.empty-state h2 {
    font-size: 2.5rem;
    color: #718096;
    margin-bottom: 1rem;
    font-weight: 700;
}

.empty-state p {
    font-size: 1.2rem;
    color: #a0aec0;
    margin-bottom: 2rem;
}

.quick-add-section {
    margin-top: 4rem;
}

.quick-add-section h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2d3748;
    font-weight: 700;
}

.quick-recipes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.quick-recipe-card {
    background: white;
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.quick-recipe-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.quick-recipe-card h4 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 700;
}

.quick-recipe-card .recipe-info {
    justify-content: center;
    margin-bottom: 1rem;
}

/* Weekly View */
.weekly-view {
    margin-top: 3rem;
    background: white;
    padding: 2rem;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
}

.weekly-view h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2d3748;
    font-weight: 700;
    text-align: center;
}

.week-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.day-column {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
}

.day-column h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: #2d3748;
    font-weight: 700;
    font-size: 1.2rem;
}

.meal-slot {
    margin-bottom: 1rem;
}

.meal-slot h4 {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
}

.planned-meal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem;
    border-radius: 10px;
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
    font-weight: 600;
}

.empty-meal {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.85rem;
    padding: 0.5rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 10% auto;
    padding: 3rem;
    border-radius: 25px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 25px 80px rgba(0,0,0,0.3);
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #a0aec0;
    float: right;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #667eea;
}

.modal-content h3 {
    margin-bottom: 2rem;
    color: #2d3748;
    font-size: 2rem;
    font-weight: 700;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-weight: 600;
    font-size: 1.1rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    font-size: 1rem;
    transition: border-color 0.3s;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        gap: 1rem;
    }

    nav h1 {
        font-size: 2rem;
    }

    .hero h2 {
        font-size: 2.5rem;
    }

    .search-section input {
        width: 100%;
        max-width: 300px;
    }

    .recipe-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .meal-plan-header {
        flex-direction: column;
        text-align: center;
    }

    .week-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 20% auto;
        padding: 2rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Features Section */
.features-section {
    margin: 4rem 0;
    text-align: center;
}

.features-section h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0,0,0,0.15);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2d3748;
    font-weight: 700;
}

.feature-card p {
    color: #4a5568;
    line-height: 1.6;
    font-weight: 500;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: white;
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s;
}

.footer-links a:hover {
    opacity: 0.8;
}

/* Empty State Enhancements */
.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Meal Stats */
.meal-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    display: block;
    color: #718096;
    font-weight: 600;
    margin-top: 0.5rem;
}

/* Tips Section */
.meal-planning-tips {
    margin-top: 4rem;
}

.meal-planning-tips h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2d3748;
    font-weight: 700;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.tip-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2d3748;
    font-weight: 700;
}

.tip-card p {
    color: #4a5568;
    line-height: 1.6;
    font-weight: 500;
}

/* Recipe Detail Enhancements */
.ingredient-tips,
.cooking-tips {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.ingredient-tips h3,
.cooking-tips h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.cooking-tips ul {
    list-style: none;
    padding: 0;
}

.cooking-tips li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
    color: #4a5568;
}

.cooking-tips li:before {
    content: "💡";
    position: absolute;
    left: 0;
}

.nutrition-note {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 10px;
    text-align: center;
}

.nutrition-note p {
    color: #718096;
    font-size: 0.9rem;
    margin: 0;
}

.recipe-suggestions {
    margin-top: 3rem;
    text-align: center;
    padding: 2rem;
    background: #f7fafc;
    border-radius: 20px;
}

.recipe-suggestions h2 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 700;
}

.recipe-suggestions p {
    color: #718096;
    margin-bottom: 1.5rem;
}

/* Loading Container */
.loading-container {
    text-align: center;
    padding: 3rem;
}

.loading-container p {
    margin-top: 1rem;
    color: #718096;
    font-weight: 500;
}

/* Error Message */
.error-message {
    text-align: center;
    color: #e53e3e;
    font-weight: 600;
    padding: 2rem;
    background: #fed7d7;
    border-radius: 10px;
    margin: 2rem 0;
}

/* Ingredient Finder Styles */
.ingredient-finder {
    margin: 3rem 0;
}

.ingredient-form {
    background: white;
    padding: 3rem;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.input-methods {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .input-methods {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }
}

@media (min-width: 1024px) {
    .input-methods {
        grid-template-columns: 1fr auto 1fr;
        align-items: center;
    }
}

.method-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.method-card:hover {
    border-color: #2d5016;
    background: #f9fafb;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.method-card h3 {
    margin-bottom: 1.5rem;
    color: #2d3748;
    font-weight: 700;
    text-align: center;
}

.method-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-weight: 700;
    font-size: 1.1rem;
}

.image-upload-area {
    position: relative;
    min-height: 200px;
    cursor: pointer;
    border-radius: 15px;
    overflow: hidden;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.image-preview {
    position: relative;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.remove-image {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-weight: bold;
}

.method-card textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    font-size: 1rem;
    resize: vertical;
    font-family: inherit;
    transition: border-color 0.3s;
}

.method-card textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters-section {
    margin: 3rem 0;
    padding: 2rem;
    background: #f7fafc;
    border-radius: 20px;
    border: 1px solid #e2e8f0;
}

.filters-section h3 {
    margin-bottom: 1.5rem;
    color: #2d3748;
    font-weight: 700;
    text-align: center;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-weight: 600;
}

.filter-group select {
    padding: 0.8rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-large {
    padding: 1.2rem 3rem;
    font-size: 1.2rem;
    margin-top: 2rem;
    width: 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

/* Browse Section */
.browse-section {
    margin: 4rem 0;
    text-align: center;
}

.browse-section h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2d3748;
    font-weight: 700;
}

.browse-filters {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.browse-filters input,
.browse-filters select {
    padding: 0.8rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    font-size: 1rem;
    background: white;
    min-width: 150px;
}

.browse-filters input:focus,
.browse-filters select:focus {
    outline: none;
    border-color: #667eea;
}

/* Recipe Card Enhancements */
.recipe-header {
    margin-bottom: 1rem;
}

.recipe-badges {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.cuisine-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.diet-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.diet-vegetarian {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.diet-vegan {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.diet-nonveg {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

/* Results Page Styles */
.results-page {
    max-width: 1400px;
}

.results-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
}

.results-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.search-summary {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.search-summary p {
    margin: 0.5rem 0;
    color: #4a5568;
    font-weight: 500;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.result-card {
    background: white;
    border-radius: 25px;
    padding: 2rem;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(0,0,0,0.15);
}

.match-indicator {
    margin-bottom: 1.5rem;
}

.match-percentage {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.percentage {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.match-label {
    color: #718096;
    font-weight: 600;
}

.match-bar {
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.match-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.ingredients-match {
    margin: 1.5rem 0;
}

.ingredients-match h4 {
    margin-bottom: 0.8rem;
    color: #2d3748;
    font-weight: 700;
    font-size: 1rem;
}

.ingredient-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.ingredient-tag {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.ingredient-tag.matched {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.ingredient-tag.missing {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #feb2b2;
}

.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-results h2 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 700;
}

.no-results p {
    color: #718096;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.no-results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Print Styles */
@media print {
    header, footer, .recipe-actions, .modal {
        display: none !important;
    }

    main {
        box-shadow: none !important;
        background: white !important;
    }

    .recipe-header {
        background: white !important;
        color: #2d3748 !important;
    }
}

/* ChefBot Chatbot Styles */
.chatbot-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.chatbot-toggle {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chef-avatar {
    position: relative;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #d4af37 0%, #8b4513 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
    transition: all 0.3s ease;
    animation: chefPulse 3s infinite;
    border: 2px solid rgba(244, 228, 188, 0.5);
}

.chef-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(212, 175, 55, 0.6);
}

.chef-face {
    font-size: 2rem;
    position: relative;
    z-index: 2;
}

.chef-hat {
    position: absolute;
    top: -10px;
    right: -5px;
    font-size: 1.2rem;
    z-index: 3;
}

@keyframes chefPulse {
    0%, 100% { box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4); }
    50% { box-shadow: 0 8px 25px rgba(212, 175, 55, 0.7); }
}

.chat-bubble {
    background: white;
    padding: 0.8rem 1.2rem;
    border-radius: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: relative;
    animation: bubbleBounce 2s infinite;
}

.chat-bubble:before {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid white;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
}

@keyframes bubbleBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.chatbot-window {
    position: absolute;
    bottom: 90px;
    right: 0;
    width: 400px;
    height: 600px;
    background: white;
    border-radius: 25px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.chatbot-header {
    background: linear-gradient(135deg, #d4af37 0%, #8b4513 100%);
    color: #2c1810;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid rgba(212, 175, 55, 0.3);
}

.bot-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bot-avatar {
    font-size: 1.5rem;
}

.bot-details h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
}

.bot-status {
    font-size: 0.8rem;
    opacity: 0.9;
}

.close-chat {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-chat:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chatbot-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.8rem;
    align-items: flex-start;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-message .message-avatar {
    background: #f7fafc;
    color: #4a5568;
}

.message-content {
    background: #f7fafc;
    padding: 1rem 1.2rem;
    border-radius: 18px;
    max-width: 80%;
    line-height: 1.5;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message-content p {
    margin: 0 0 0.5rem 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 0.5rem 0;
    padding-left: 1.2rem;
}

.typing-indicator .message-content {
    padding: 1rem;
}

.typing-dots {
    display: flex;
    gap: 0.3rem;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typingDots 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDots {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}

.chatbot-input {
    border-top: 1px solid #e2e8f0;
    padding: 1rem;
}

.quick-questions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.quick-btn {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    color: #667eea;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
}

.input-area {
    display: flex;
    gap: 0.8rem;
    align-items: center;
}

#chat-input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    outline: none;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

#chat-input:focus {
    border-color: #667eea;
}

.send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Auth Page Styles */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1200px;
    width: 100%;
    background: white;
    border-radius: 30px;
    overflow: hidden;
    box-shadow: 0 25px 80px rgba(0,0,0,0.2);
}

.auth-card {
    padding: 3rem;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-section h1 {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.logo-section p {
    color: #718096;
    font-weight: 500;
}

.auth-form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-form-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.auth-form-header p {
    color: #718096;
    font-weight: 500;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.auth-switch {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.auth-switch a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

.auth-features {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.auth-features h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 700;
}

.features-list {
    display: grid;
    gap: 0.8rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #4a5568;
    font-weight: 500;
}

.feature-icon {
    font-size: 1.2rem;
}

.auth-visual {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.visual-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.visual-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.visual-text p {
    font-size: 1.2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.floating-item {
    position: absolute;
    font-size: 3rem;
    opacity: 0.3;
    animation: authFloat 15s infinite linear;
    animation-delay: var(--delay);
}

@keyframes authFloat {
    0% { transform: translateY(100vh) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

.auth-footer {
    text-align: center;
    padding: 2rem;
    color: white;
    background: rgba(0,0,0,0.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-radius: 0 0 20px 20px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .input-methods-grid {
        grid-template-columns: 1fr;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .browse-filters {
        flex-direction: column;
        align-items: center;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .chatbot-window {
        width: 90vw;
        height: 70vh;
        right: 5vw;
    }

    .auth-container {
        grid-template-columns: 1fr;
        max-width: 500px;
    }

    .auth-visual {
        display: none;
    }
}

/* Cute Nutrition Box */
.nutrition-section {
    margin: 2rem 0;
}

.nutrition-box {
    background: linear-gradient(135deg, rgba(244, 228, 188, 0.9), rgba(255, 255, 255, 0.95));
    border: 3px solid #d4af37;
    border-radius: 25px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
    position: relative;
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
}

.nutrition-box::before {
    content: "🍎";
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 3rem;
    opacity: 0.1;
    transform: rotate(15deg);
}

.nutrition-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.nutrition-header h3 {
    font-size: 1.5rem;
    color: #8b4513;
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.nutrition-subtitle {
    font-size: 0.9rem;
    color: #5d4037;
    font-style: italic;
}

.nutrition-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.nutrition-item {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.nutrition-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
    border-color: #d4af37;
}

.nutrition-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.nutrition-label {
    display: block;
    color: #8b4513;
    font-weight: 600;
    margin-bottom: 0.3rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Lato', sans-serif;
}

.nutrition-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c1810;
    font-family: 'Playfair Display', serif;
}

.nutrition-unit {
    font-size: 0.8rem;
    color: #5d4037;
    font-weight: 400;
}

@media (min-width: 768px) {
    .nutrition-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .nutrition-item {
        padding: 1.2rem;
    }

    .nutrition-value {
        font-size: 1.8rem;
    }
}

/* Favorite Button */
.btn-favorite {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(212, 175, 55, 0.3);
    color: #8b4513;
    padding: 0.6rem;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
}

.btn-favorite:hover {
    transform: scale(1.1);
    border-color: #d4af37;
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.btn-favorite.favorited {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-color: #ff6b6b;
    color: white;
}

/* Recipe Actions Layout */
.recipe-actions {
    display: flex;
    gap: 0.8rem;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .recipe-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .recipe-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .btn-favorite {
        width: 45px;
        align-self: center;
    }
}

/* Browse Page Styles */
.browse-header, .favorites-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
}

.browse-title, .favorites-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c1810;
    font-family: 'Playfair Display', serif;
    margin-bottom: 1rem;
}

.browse-subtitle, .favorites-subtitle {
    font-size: 1.2rem;
    color: #5d4037;
    font-family: 'Lato', sans-serif;
}

.browse-filters-section {
    background: linear-gradient(135deg, rgba(244, 228, 188, 0.9), rgba(255, 255, 255, 0.95));
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px solid rgba(212, 175, 55, 0.2);
}

.search-bar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.search-input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 25px;
    font-size: 1rem;
    font-family: 'Lato', sans-serif;
    background: rgba(255, 255, 255, 0.9);
}

.search-input:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.search-btn {
    background: linear-gradient(135deg, #d4af37 0%, #8b4513 100%);
    border: none;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.filters-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 768px) {
    .filters-row {
        grid-template-columns: repeat(3, 1fr);
    }
}

.filter-select {
    padding: 0.8rem 1rem;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 15px;
    font-size: 1rem;
    font-family: 'Lato', sans-serif;
    background: rgba(255, 255, 255, 0.9);
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #d4af37;
}

/* Favorites Page Styles */
.favorites-stats {
    margin-top: 1rem;
}

.favorites-stats .stat-item {
    display: inline-block;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(139, 69, 19, 0.05));
    padding: 1rem 2rem;
    border-radius: 20px;
    border: 2px solid rgba(212, 175, 55, 0.3);
}

.favorites-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: #d4af37;
    font-family: 'Playfair Display', serif;
}

.favorites-stats .stat-label {
    font-size: 0.9rem;
    color: #8b4513;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.favorites-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 640px) {
    .favorites-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .favorites-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.empty-favorites {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, rgba(244, 228, 188, 0.9), rgba(255, 255, 255, 0.95));
    border-radius: 25px;
    border: 2px solid rgba(212, 175, 55, 0.2);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-favorites h3 {
    font-size: 1.8rem;
    color: #2c1810;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', serif;
}

.empty-favorites p {
    font-size: 1.1rem;
    color: #5d4037;
    margin-bottom: 2rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.unfavorite-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.unfavorite-btn:hover {
    background: rgba(255, 107, 107, 0.1);
    transform: scale(1.1);
}

/* Quick Actions Styles */
.quick-actions-section {
    margin: 3rem 0;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
}

@media (min-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .quick-actions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.action-card {
    background: linear-gradient(135deg, rgba(244, 228, 188, 0.9), rgba(255, 255, 255, 0.95));
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 2px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(139, 69, 19, 0.15);
    border-color: #d4af37;
}

.action-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.action-card h3 {
    font-size: 1.5rem;
    color: #2c1810;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', serif;
    font-weight: 600;
}

.action-card p {
    color: #5d4037;
    margin-bottom: 1.5rem;
    font-family: 'Lato', sans-serif;
    line-height: 1.6;
}

.action-card .btn {
    margin-top: auto;
}
