<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if mode == 'register' %}Join{% else %}Welcome to{% endif %} MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 mb-6">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-16 w-16">
                    <div>
                        <h1 class="text-3xl font-serif font-bold text-rich-green">MealMind</h1>
                        <p class="text-sm text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                {% if mode == 'register' %}
                <h2 class="text-2xl font-serif font-semibold text-rich-green mb-2">Create Your Account</h2>
                <p class="text-soft-gray">Join thousands of home cooks discovering amazing recipes!</p>
                {% else %}
                <h2 class="text-2xl font-serif font-semibold text-rich-green mb-2">Welcome Back</h2>
                <p class="text-soft-gray">Sign in to continue your culinary journey</p>
                {% endif %}
            </div>

            <!-- Form -->
            <div class="bg-white rounded-2xl p-8 border border-rich-green/10 shadow-lg">
                {% if mode == 'register' %}
                <form action="{{ url_for('register') }}" method="POST" class="space-y-5">
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-rich-green mb-2">👤 Full Name</label>
                        <input type="text" id="full_name" name="full_name" required
                               placeholder="Enter your full name"
                               class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                    </div>

                    <div>
                        <label for="username" class="block text-sm font-medium text-rich-green mb-2">🏷️ Username</label>
                        <input type="text" id="username" name="username" required
                               placeholder="Choose a username"
                               class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-rich-green mb-2">📧 Email Address</label>
                        <input type="email" id="email" name="email" required
                               placeholder="Enter your email"
                               class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-rich-green mb-2">🔒 Password</label>
                        <input type="password" id="password" name="password" required
                               placeholder="Create a strong password"
                               class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                        <p class="text-xs text-soft-gray mt-1">Password should be at least 8 characters long</p>
                    </div>

                    <button type="submit" class="w-full bg-rich-green text-organic-beige py-3 px-6 rounded-xl font-semibold hover:bg-soft-green transition-colors">
                        🚀 Create Account
                    </button>
                </form>

                <div class="text-center mt-6 pt-6 border-t border-rich-green/10">
                    <p class="text-soft-gray">Already have an account?
                        <a href="{{ url_for('login') }}" class="text-rich-green hover:text-soft-green font-medium transition-colors">Sign in here</a>
                    </p>
                </div>

                {% else %}
                <form action="{{ url_for('login') }}" method="POST" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-rich-green mb-2">📧 Email Address</label>
                        <input type="email" id="email" name="email" required
                               placeholder="Enter your email"
                               class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-rich-green mb-2">🔒 Password</label>
                        <input type="password" id="password" name="password" required
                               placeholder="Enter your password"
                               class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                    </div>

                    <button type="submit" class="w-full bg-rich-green text-organic-beige py-3 px-6 rounded-xl font-semibold hover:bg-soft-green transition-colors">
                        🍳 Sign In
                    </button>
                </form>

                <div class="text-center mt-6 pt-6 border-t border-rich-green/10">
                    <p class="text-soft-gray">New to MealMind?
                        <a href="{{ url_for('register') }}" class="text-rich-green hover:text-soft-green font-medium transition-colors">Create an account</a>
                    </p>
                </div>
                {% endif %}
            </div>

        </div>
    </div>
    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
