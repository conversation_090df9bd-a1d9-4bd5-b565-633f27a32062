<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Recipes - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Browse Header -->
        <section class="py-12 lg:py-16 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl lg:text-5xl font-serif font-bold text-rich-green mb-4">Discover Amazing Recipes</h1>
                <p class="text-lg text-soft-gray max-w-2xl mx-auto">Explore our collection of delicious recipes from around the world</p>
            </div>
        </section>

        <!-- Search and Filters -->
        <section class="py-8 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Search Bar -->
                <div class="mb-6">
                    <div class="flex max-w-2xl mx-auto gap-3">
                        <input type="text" id="search-input" placeholder="Search recipes by name or ingredients..."
                               class="flex-1 pl-4 pr-4 py-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green">
                        <button id="search-button" onclick="performSearch()" class="bg-rich-green text-organic-beige px-6 py-3 rounded-xl font-semibold hover:bg-soft-green transition-colors flex items-center gap-2">
                            🔍 Search
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
                    <select id="cuisine-filter" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green bg-white">
                        <option value="">All Cuisines</option>
                        {% for cuisine in cuisines %}
                        <option value="{{ cuisine }}">{{ cuisine }}</option>
                        {% endfor %}
                    </select>

                    <select id="diet-filter" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green bg-white">
                        <option value="">All Diet Types</option>
                        {% for diet in diet_types %}
                        <option value="{{ diet }}">{{ diet }}</option>
                        {% endfor %}
                    </select>

                    <select id="difficulty-filter" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green bg-white">
                        <option value="">All Difficulties</option>
                        <option value="Easy">Easy</option>
                        <option value="Medium">Medium</option>
                        <option value="Hard">Hard</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- Recipe Grid -->
        <section class="py-12 bg-organic-beige">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div id="recipes-container" class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {% for recipe in recipes %}
                    <div class="bg-white rounded-2xl p-6 border border-rich-green/10 hover:border-rich-green/20 transition-colors recipe-card recipe-card-organic" data-recipe-id="{{ recipe.id }}" data-cuisine="{{ recipe.cuisine }}" data-diet="{{ recipe.diet_type }}" data-difficulty="{{ recipe.difficulty }}" data-name="{{ recipe.name.lower() }}">
                        <div class="mb-4">
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3">{{ recipe.name }}</h3>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="ingredient-tag cuisine-badge">{{ recipe.cuisine }}</span>
                                <span class="ingredient-tag diet-badge">{{ recipe.diet_type }}</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-2 text-sm text-soft-gray mb-4">
                            <div class="flex items-center">
                                <span class="mr-1">⏱️</span>
                                <span>{{ recipe.prep_time }} min</span>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1">👥</span>
                                <span>{{ recipe.servings }} servings</span>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1">🔥</span>
                                <span>{{ recipe.calories }} cal</span>
                            </div>
                            <div class="flex items-center info-item">
                                <span class="mr-1">📊</span>
                                <span>{{ recipe.difficulty }}</span>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex space-x-2">
                            <a href="{{ url_for('recipe_detail', recipe_id=recipe.id) }}"
                               class="flex-1 bg-rich-green text-organic-beige py-2 px-4 rounded-lg text-sm font-medium hover:bg-soft-green transition-colors text-center">
                                👁️ View Recipe
                            </a>
                            <button onclick="addToMealPlan({{ recipe.id }})"
                                    class="flex-1 border border-rich-green text-rich-green py-2 px-4 rounded-lg text-sm font-medium hover:bg-rich-green hover:text-organic-beige transition-colors">
                                ➕ Add to Plan
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- View More Button -->
                <div class="text-center mt-12">
                    <button id="view-more-btn" onclick="loadMoreRecipes()" class="bg-rich-green text-organic-beige px-8 py-3 rounded-xl font-semibold hover:bg-soft-green transition-colors inline-flex items-center gap-2">
                        <span>View More Recipes</span>
                        <span>⬇️</span>
                    </button>
                    <div id="loading-indicator" class="hidden mt-4">
                        <div class="inline-flex items-center gap-2 text-rich-green">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-rich-green"></div>
                            <span>Loading more recipes...</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>
    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>





    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        let currentPage = 1;
        let recipesPerPage = 9;
        let allRecipes = [];
        let filteredRecipes = [];
        let isSearchMode = false;

        // Initialize browse page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Store all recipes for pagination
            allRecipes = Array.from(document.querySelectorAll('.recipe-card'));
            filteredRecipes = [...allRecipes];

            // Show initial 9 recipes
            showRecipes();

            // Add filter event listeners
            document.getElementById('cuisine-filter').addEventListener('change', applyFilters);
            document.getElementById('diet-filter').addEventListener('change', applyFilters);
            document.getElementById('difficulty-filter').addEventListener('change', applyFilters);

            // Add search functionality
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Mobile menu toggle
            document.querySelector('.mobile-menu-toggle').addEventListener('click', function() {
                document.querySelector('.nav-menu').classList.toggle('active');
            });
        });

        function performSearch() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase().trim();

            if (searchTerm === '') {
                // Reset to show all recipes with current filters
                isSearchMode = false;
                applyFilters();
                return;
            }

            isSearchMode = true;
            currentPage = 1;

            // Filter recipes based on search term
            filteredRecipes = allRecipes.filter(card => {
                const recipeName = card.querySelector('h3').textContent.toLowerCase();
                const ingredients = card.dataset.name || '';
                return recipeName.includes(searchTerm) || ingredients.includes(searchTerm);
            });

            // Apply additional filters if any are selected
            applyCurrentFilters();
            showRecipes();
        }

        function applyFilters() {
            if (isSearchMode) {
                // If in search mode, apply filters to search results
                applyCurrentFilters();
            } else {
                // Normal filter mode
                currentPage = 1;
                filteredRecipes = [...allRecipes];
                applyCurrentFilters();
            }
            showRecipes();
        }

        function applyCurrentFilters() {
            const cuisineFilter = document.getElementById('cuisine-filter').value;
            const dietFilter = document.getElementById('diet-filter').value;
            const difficultyFilter = document.getElementById('difficulty-filter').value;

            if (cuisineFilter || dietFilter || difficultyFilter) {
                filteredRecipes = filteredRecipes.filter(card => {
                    const cuisine = card.dataset.cuisine;
                    const diet = card.dataset.diet;
                    const difficulty = card.dataset.difficulty;

                    const matchesCuisine = !cuisineFilter || cuisine === cuisineFilter;
                    const matchesDiet = !dietFilter || diet === dietFilter;
                    const matchesDifficulty = !difficultyFilter || difficulty === difficultyFilter;

                    return matchesCuisine && matchesDiet && matchesDifficulty;
                });
            }
        }

        function showRecipes() {
            // Hide all recipes first
            allRecipes.forEach(card => card.style.display = 'none');

            // Calculate which recipes to show
            const startIndex = 0;
            const endIndex = currentPage * recipesPerPage;
            const recipesToShow = filteredRecipes.slice(startIndex, endIndex);

            // Show the recipes for current page
            recipesToShow.forEach(card => card.style.display = 'block');

            // Update View More button
            const viewMoreBtn = document.getElementById('view-more-btn');
            if (endIndex >= filteredRecipes.length) {
                viewMoreBtn.style.display = 'none';
            } else {
                viewMoreBtn.style.display = 'inline-flex';
                const remaining = filteredRecipes.length - endIndex;
                viewMoreBtn.querySelector('span').textContent = `View More Recipes (${remaining} remaining)`;
            }

            // Show message if no recipes found
            if (filteredRecipes.length === 0) {
                showNoResultsMessage();
            } else {
                hideNoResultsMessage();
            }
        }

        function loadMoreRecipes() {
            const loadingIndicator = document.getElementById('loading-indicator');
            const viewMoreBtn = document.getElementById('view-more-btn');

            // Show loading indicator
            loadingIndicator.classList.remove('hidden');
            viewMoreBtn.style.display = 'none';

            // Simulate loading delay
            setTimeout(() => {
                currentPage++;
                showRecipes();
                loadingIndicator.classList.add('hidden');
            }, 500);
        }

        function showNoResultsMessage() {
            let noResultsDiv = document.getElementById('no-results-message');
            if (!noResultsDiv) {
                noResultsDiv = document.createElement('div');
                noResultsDiv.id = 'no-results-message';
                noResultsDiv.className = 'text-center py-12';
                noResultsDiv.innerHTML = `
                    <div class="text-6xl mb-4">🔍</div>
                    <h3 class="text-2xl font-serif font-semibold text-rich-green mb-2">No Recipes Found</h3>
                    <p class="text-soft-gray">Try adjusting your search terms or filters</p>
                `;
                document.getElementById('recipes-container').parentNode.insertBefore(noResultsDiv, document.getElementById('recipes-container').nextSibling);
            }
            noResultsDiv.style.display = 'block';
        }

        function hideNoResultsMessage() {
            const noResultsDiv = document.getElementById('no-results-message');
            if (noResultsDiv) {
                noResultsDiv.style.display = 'none';
            }
        }

        // Show login prompt for non-logged-in users
        function showLoginPrompt() {
            if (confirm('Please log in to save favorites. Would you like to go to the login page?')) {
                window.location.href = '/login';
            }
        }

        // Enhanced favorite toggle with visual feedback
        function toggleFavorite(recipeId) {
            fetch('/toggle-favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ recipe_id: recipeId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'error');
                    return;
                }

                const btn = document.getElementById(`fav-btn-${recipeId}`);
                if (btn) {
                    if (data.is_favorite) {
                        btn.innerHTML = '❤️';
                        btn.classList.add('favorited');
                    } else {
                        btn.innerHTML = '🤍';
                        btn.classList.remove('favorited');
                    }
                }

                showNotification(data.message, 'success');
            })
            .catch(error => {
                console.error('Error toggling favorite:', error);
                showNotification('Error updating favorite', 'error');
            });
        }
    </script>

    <!-- Mobile Menu JavaScript -->
    <script>
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
