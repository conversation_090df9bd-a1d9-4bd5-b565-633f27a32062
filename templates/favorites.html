<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Favorites - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Floating Background Elements -->
    <div class="bg-elements">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
        <div class="floating-shape shape-4"></div>
    </div>

    <header class="modern-header">
        <nav class="nav-container">
            <div class="nav-brand">
                <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" style="height: 40px; width: 40px; margin-right: 12px;">
                <div>
                    <h1>MealMind</h1>
                    <span class="brand-tagline">Think Smart. Eat Better.</span>
                </div>
            </div>
            <ul class="nav-menu">
                <li><a href="{{ url_for('browse_recipes') }}" class="nav-link">🔍 Discover</a></li>
                <li><a href="{{ url_for('index') }}" class="nav-link">🏠 Home</a></li>
                <li><a href="{{ url_for('meal_plan') }}" class="nav-link">📅 Meal Plan</a></li>
                <li><a href="{{ url_for('favorites') }}" class="nav-link active">❤️ Favorites</a></li>
                <li><a href="{{ url_for('profile') }}" class="nav-link">👤 Profile</a></li>
                <li><a href="{{ url_for('logout') }}" class="nav-link">🚪 Logout</a></li>
            </ul>
            <button class="mobile-menu-toggle">☰</button>
        </nav>
    </header>

    <main class="favorites-main">
        <div class="container">
            <!-- Favorites Header -->
            <section class="favorites-header">
                <h1 class="favorites-title">❤️ My Favorite Recipes</h1>
                <p class="favorites-subtitle">Your saved recipes collection</p>
                <div class="favorites-stats">
                    <span class="stat-item">
                        <span class="stat-number">{{ favorite_recipes|length }}</span>
                        <span class="stat-label">Saved Recipes</span>
                    </span>
                </div>
            </section>

            <!-- Favorites Content -->
            <section class="favorites-content">
                {% if favorite_recipes %}
                <div class="favorites-grid">
                    {% for recipe in favorite_recipes %}
                    <div class="favorite-card">
                        <div class="recipe-header">
                            <h3>{{ recipe.name }}</h3>
                            <button onclick="toggleFavorite({{ recipe.id }})" class="unfavorite-btn" title="Remove from favorites">
                                💔
                            </button>
                        </div>

                        <div class="recipe-badges">
                            <span class="cuisine-badge">{{ recipe.cuisine }}</span>
                            <span class="diet-badge diet-{{ recipe.diet_type.lower().replace('-', '').replace(' ', '') }}">{{ recipe.diet_type }}</span>
                        </div>

                        <div class="recipe-info">
                            <span class="info-item">⏱️ {{ recipe.prep_time }} min</span>
                            <span class="info-item">👥 {{ recipe.servings }} servings</span>
                            <span class="info-item">🔥 {{ recipe.calories }} cal</span>
                            <span class="info-item">📊 {{ recipe.difficulty }}</span>
                        </div>

                        <div class="ingredients-preview">
                            <strong>🥘 Key Ingredients:</strong>
                            <ul>
                                {% if recipe.ingredients is mapping %}
                                    {% set ingredient_count = 0 %}
                                    {% for category, items in recipe.ingredients.items() %}
                                        {% for ingredient in items %}
                                            {% if ingredient_count < 3 %}
                                                <li>{{ ingredient }}</li>
                                                {% set ingredient_count = ingredient_count + 1 %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endfor %}
                                    {% set total_ingredients = recipe.ingredients.values() | sum(start=[]) | length %}
                                    {% if total_ingredients > 3 %}
                                    <li>... and {{ total_ingredients - 3 }} more</li>
                                    {% endif %}
                                {% else %}
                                    {% for ingredient in recipe.ingredients[:3] %}
                                    <li>{{ ingredient }}</li>
                                    {% endfor %}
                                    {% if recipe.ingredients|length > 3 %}
                                    <li>... and {{ recipe.ingredients|length - 3 }} more</li>
                                    {% endif %}
                                {% endif %}
                            </ul>
                        </div>

                        <div class="recipe-actions">
                            <a href="{{ url_for('recipe_detail', recipe_id=recipe.id) }}" class="btn btn-primary">👁️ View Recipe</a>
                            <button onclick="addToMealPlan({{ recipe.id }})" class="btn btn-secondary">➕ Add to Plan</button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="empty-favorites">
                    <div class="empty-icon">💔</div>
                    <h3>No favorite recipes yet</h3>
                    <p>Start exploring recipes and save your favorites!</p>
                    <div class="empty-actions">
                        <a href="{{ url_for('browse_recipes') }}" class="btn btn-primary">🔍 Discover Recipes</a>
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">🏠 Find by Ingredients</a>
                    </div>
                </div>
                {% endif %}
            </section>
        </div>
    </main>
    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>





    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Initialize favorites page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            document.querySelector('.mobile-menu-toggle').addEventListener('click', function() {
                document.querySelector('.nav-menu').classList.toggle('active');
            });
        });

        // Enhanced favorite toggle with visual feedback
        function toggleFavorite(recipeId) {
            fetch('/toggle-favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ recipe_id: recipeId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'error');
                    return;
                }

                // If removed from favorites, remove the card from the page
                if (!data.is_favorite) {
                    const card = document.querySelector(`[onclick="toggleFavorite(${recipeId})"]`).closest('.favorite-card');
                    if (card) {
                        card.style.transition = 'all 0.3s ease';
                        card.style.opacity = '0';
                        card.style.transform = 'scale(0.8)';
                        setTimeout(() => {
                            card.remove();
                            // Update favorites count
                            const countElement = document.querySelector('.stat-number');
                            if (countElement) {
                                const currentCount = parseInt(countElement.textContent);
                                countElement.textContent = currentCount - 1;
                            }
                            // Check if no favorites left
                            const remainingCards = document.querySelectorAll('.favorite-card');
                            if (remainingCards.length === 0) {
                                location.reload(); // Reload to show empty state
                            }
                        }, 300);
                    }
                }

                showNotification(data.message, 'success');
            })
            .catch(error => {
                console.error('Error toggling favorite:', error);
                showNotification('Error updating favorite', 'error');
            });
        }
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
