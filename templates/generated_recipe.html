<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Custom Recipe - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="/static/organic-overrides.css">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="/static/logo.svg" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="/browse" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="/smart-recipes" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Smart Recipes</a>
                    <a href="/meal-plan" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                </div>
            </div>
        </nav>
    </header>

    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Banner -->
        <div class="bg-light-sage border border-rich-green/20 rounded-2xl p-6 mb-8">
            <div class="flex items-center space-x-3">
                <div class="text-3xl">🎉</div>
                <div>
                    <h2 class="text-xl font-serif font-bold text-rich-green">Recipe Generated Successfully!</h2>
                    <p class="text-soft-gray">Here's a custom recipe created using only your ingredients</p>
                </div>
            </div>
        </div>

        <!-- Recipe Card -->
        <div class="bg-white rounded-3xl shadow-lg overflow-hidden">
            <!-- Recipe Header -->
            <div class="bg-gradient-to-r from-rich-green to-soft-green text-white p-8">
                <div class="flex items-start justify-between">
                    <div>
                        <h1 class="text-3xl font-serif font-bold mb-2">{{ recipe.name }}</h1>
                        <p class="text-white/80 text-lg">Custom recipe using your ingredients</p>
                    </div>
                    <div class="text-right">
                        <div class="bg-white/20 rounded-full px-4 py-2 mb-2">
                            <span class="text-sm font-medium">{{ recipe.cuisine }} Style</span>
                        </div>
                        <div class="bg-white/20 rounded-full px-4 py-2">
                            <span class="text-sm font-medium">{{ recipe.diet_type }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recipe Info -->
            <div class="p-8">
                <div class="grid md:grid-cols-4 gap-6 mb-8">
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">⏱️</span>
                        </div>
                        <div class="text-sm text-soft-gray">Prep Time</div>
                        <div class="font-semibold text-rich-green">{{ recipe.prep_time }} min</div>
                    </div>
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">👥</span>
                        </div>
                        <div class="text-sm text-soft-gray">Servings</div>
                        <div class="font-semibold text-rich-green">{{ recipe.servings }}</div>
                    </div>
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">🔥</span>
                        </div>
                        <div class="text-sm text-soft-gray">Calories</div>
                        <div class="font-semibold text-rich-green">{{ recipe.calories }}</div>
                    </div>
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="text-sm text-soft-gray">Difficulty</div>
                        <div class="font-semibold text-rich-green">{{ recipe.difficulty }}</div>
                    </div>
                </div>

                <!-- Your Ingredients -->
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">🥗</span>
                        Your Ingredients
                    </h3>
                    <div class="bg-light-sage rounded-2xl p-6">
                        <div class="flex flex-wrap gap-2">
                            {% for ingredient in user_ingredients %}
                            <span class="bg-white px-4 py-2 rounded-full text-sm font-medium text-rich-green border border-rich-green/20">
                                {{ ingredient }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">👨‍🍳</span>
                        Cooking Instructions
                    </h3>
                    <div class="space-y-4">
                        {% for instruction in recipe.instructions %}
                        <div class="flex items-start space-x-4">
                            <div class="bg-rich-green text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                                {{ loop.index }}
                            </div>
                            <p class="text-soft-gray leading-relaxed">{{ instruction }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Cooking Tips -->
                {% if recipe.tips %}
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">💡</span>
                        Cooking Tips
                    </h3>
                    <div class="bg-organic-cream rounded-2xl p-6">
                        <p class="text-soft-gray leading-relaxed">{{ recipe.tips }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-rich-green/10">
                    <button onclick="window.print()" class="bg-rich-green text-white px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors flex items-center justify-center">
                        <span class="mr-2">🖨️</span>
                        Print Recipe
                    </button>
                    <a href="/smart-recipes" class="border-2 border-rich-green text-rich-green px-6 py-3 rounded-full font-semibold hover:bg-rich-green hover:text-white transition-colors text-center">
                        Generate Another Recipe
                    </a>
                    <a href="/browse" class="text-rich-green hover:text-soft-green transition-colors px-6 py-3 font-medium text-center">
                        Browse More Recipes
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <img src="/static/logo.svg" alt="MealMind Logo" class="h-10 w-10">
                <div>
                    <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                    <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                </div>
            </div>
            <p class="text-organic-beige/60 text-sm">
                © 2025 MealMind. Made with 💚 for food lovers everywhere.
            </p>
        </div>
    </footer>

    <script src="/static/script.js"></script>
</body>
</html>
