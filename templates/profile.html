<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Profile Header -->
        <section class="py-12 lg:py-16 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white rounded-2xl p-8 border border-rich-green/10 shadow-lg">
                    <div class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
                        <!-- Avatar -->
                        <div class="relative">
                            <div class="w-24 h-24 bg-rich-green rounded-full flex items-center justify-center text-organic-beige text-3xl font-serif font-bold">
                                {{ user.full_name[0].upper() }}
                            </div>
                            <button class="absolute -bottom-2 -right-2 bg-soft-green text-organic-beige w-8 h-8 rounded-full hover:bg-rich-green transition-colors">
                                📷
                            </button>
                        </div>

                        <!-- Profile Info -->
                        <div class="flex-1 text-center md:text-left">
                            <h1 class="text-3xl font-serif font-bold text-rich-green mb-2">{{ user.full_name }}</h1>
                            <p class="text-lg text-soft-gray mb-1">@{{ user.username }}</p>
                            <p class="text-sm text-soft-gray">Member since {{ user.created_at.strftime('%B %Y') }}</p>
                        </div>

                        <!-- Stats -->
                        <div class="grid grid-cols-3 gap-6 text-center">
                            <div>
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ favorite_recipes|length }}</div>
                                <div class="text-sm text-soft-gray">Favorites</div>
                            </div>
                            <div>
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ profile.favorite_cuisines|length if profile.favorite_cuisines else 0 }}</div>
                                <div class="text-sm text-soft-gray">Cuisines</div>
                            </div>
                            <div>
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ profile.cooking_skill if profile.cooking_skill else 'Beginner' }}</div>
                                <div class="text-sm text-soft-gray">Level</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Profile Content -->
        <section class="py-12 bg-organic-beige">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid lg:grid-cols-4 gap-8">
                    <!-- Sidebar -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-2xl p-6 border border-rich-green/10 sticky top-24">
                            <h3 class="text-lg font-serif font-semibold text-rich-green mb-4">Profile Menu</h3>
                            <nav class="space-y-2">
                                <button class="w-full text-left px-4 py-3 rounded-lg bg-light-sage text-rich-green font-medium" data-tab="favorites">
                                    ❤️ Favorite Recipes
                                </button>
                                <button class="w-full text-left px-4 py-3 rounded-lg text-soft-gray hover:bg-light-sage hover:text-rich-green transition-colors" data-tab="preferences">
                                    ⚙️ Preferences
                                </button>
                                <button class="w-full text-left px-4 py-3 rounded-lg text-soft-gray hover:bg-light-sage hover:text-rich-green transition-colors" data-tab="dietary">
                                    🥗 Dietary Info
                                </button>
                                <button class="w-full text-left px-4 py-3 rounded-lg text-soft-gray hover:bg-light-sage hover:text-rich-green transition-colors" data-tab="account">
                                    👤 Account Settings
                                </button>
                            </nav>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="lg:col-span-3">
                        <!-- Favorite Recipes Tab -->
                        <div class="tab-content" id="favorites-tab">
                            <div class="bg-white rounded-2xl p-8 border border-rich-green/10">
                                <div class="mb-8">
                                    <h2 class="text-2xl font-serif font-bold text-rich-green mb-2">❤️ Your Favorite Recipes</h2>
                                    <p class="text-soft-gray">Recipes you've saved for later</p>
                                </div>

                                {% if favorite_recipes %}
                                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {% for recipe in favorite_recipes %}
                                    <div class="bg-organic-beige rounded-xl p-6 border border-rich-green/10 hover:border-rich-green/20 transition-colors">
                                        <div class="flex justify-between items-start mb-3">
                                            <h3 class="text-lg font-serif font-semibold text-rich-green">{{ recipe.name }}</h3>
                                            <button class="text-red-500 hover:text-red-700 transition-colors" onclick="toggleFavorite({{ recipe.id }})">💔</button>
                                        </div>
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            <span class="ingredient-tag">{{ recipe.cuisine }}</span>
                                            <span class="ingredient-tag">{{ recipe.diet_type }}</span>
                                        </div>
                                        <div class="grid grid-cols-3 gap-2 text-sm text-soft-gray mb-4">
                                            <div>⏱️ {{ recipe.prep_time }}m</div>
                                            <div>🔥 {{ recipe.calories }}cal</div>
                                            <div>📊 {{ recipe.difficulty }}</div>
                                        </div>
                                        <a href="{{ url_for('recipe_detail', recipe_id=recipe.id) }}"
                                           class="w-full bg-rich-green text-organic-beige py-2 px-4 rounded-lg text-sm font-medium hover:bg-soft-green transition-colors text-center block">
                                            View Recipe
                                        </a>
                                    </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="empty-favorites">
                        <div class="empty-icon">💔</div>
                        <h3>No favorite recipes yet</h3>
                        <p>Start exploring recipes and save your favorites!</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">Discover Recipes</a>
                    </div>
                    {% endif %}
                </div>

                <!-- Preferences Tab -->
                <div class="tab-content" id="preferences-tab">
                    <div class="tab-header">
                        <h2>⚙️ Cooking Preferences</h2>
                        <p>Customize your cooking experience</p>
                    </div>

                    <form class="preferences-form">
                        <div class="form-section">
                            <h3>🍽️ Favorite Cuisines</h3>
                            <div class="cuisine-grid">
                                {% for cuisine in ['Italian', 'Indian', 'Chinese', 'Japanese', 'Korean', 'American', 'Thai', 'Mediterranean', 'Fusion'] %}
                                <label class="cuisine-checkbox">
                                    <input type="checkbox" name="favorite_cuisines" value="{{ cuisine }}"
                                           {% if cuisine in profile.favorite_cuisines %}checked{% endif %}>
                                    <span class="checkmark">{{ cuisine }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>👨‍🍳 Cooking Skill Level</h3>
                            <div class="skill-selector">
                                {% for skill in ['Beginner', 'Intermediate', 'Advanced', 'Expert'] %}
                                <label class="skill-option">
                                    <input type="radio" name="cooking_skill" value="{{ skill }}"
                                           {% if skill == profile.cooking_skill %}checked{% endif %}>
                                    <span class="skill-label">{{ skill }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Save Preferences</button>
                    </form>
                </div>

                <!-- Dietary Info Tab -->
                <div class="tab-content" id="dietary-tab">
                    <div class="tab-header">
                        <h2>🥗 Dietary Information</h2>
                        <p>Help us suggest better recipes for you</p>
                    </div>

                    <form class="dietary-form">
                        <div class="form-section">
                            <h3>🌱 Dietary Preferences</h3>
                            <div class="diet-grid">
                                {% for diet in ['Vegetarian', 'Vegan', 'Gluten-Free', 'Keto', 'Paleo', 'Low-Carb', 'High-Protein'] %}
                                <label class="diet-checkbox">
                                    <input type="checkbox" name="dietary_preferences" value="{{ diet }}"
                                           {% if diet in profile.dietary_preferences %}checked{% endif %}>
                                    <span class="checkmark">{{ diet }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>⚠️ Allergies & Restrictions</h3>
                            <div class="allergies-grid">
                                {% for allergy in ['Nuts', 'Dairy', 'Eggs', 'Shellfish', 'Soy', 'Wheat', 'Fish'] %}
                                <label class="allergy-checkbox">
                                    <input type="checkbox" name="allergies" value="{{ allergy }}"
                                           {% if allergy in profile.allergies %}checked{% endif %}>
                                    <span class="checkmark">{{ allergy }}</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Save Dietary Info</button>
                    </form>
                </div>

                <!-- Account Settings Tab -->
                <div class="tab-content" id="account-tab">
                    <div class="tab-header">
                        <h2>👤 Account Settings</h2>
                        <p>Manage your account information</p>
                    </div>

                    <form class="account-form">
                        <div class="form-group">
                            <label for="full_name">Full Name</label>
                            <input type="text" id="full_name" name="full_name" value="{{ user.full_name }}">
                        </div>

                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" name="username" value="{{ user.username }}">
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" value="{{ user.email }}">
                        </div>

                        <button type="submit" class="btn btn-primary">Update Account</button>
                    </form>

                    <div class="danger-zone">
                        <h3>⚠️ Danger Zone</h3>
                        <p>These actions cannot be undone</p>
                        <button class="btn btn-danger">Delete Account</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Profile tab switching
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const tabId = this.dataset.tab + '-tab';

                // Remove active class from all menu items and tabs
                document.querySelectorAll('.menu-item').forEach(mi => mi.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));

                // Add active class to clicked item and corresponding tab
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Form submissions
        document.querySelector('.preferences-form').addEventListener('submit', function(e) {
            e.preventDefault();
            // Handle preferences update
            showNotification('Preferences updated successfully!', 'success');
        });

        document.querySelector('.dietary-form').addEventListener('submit', function(e) {
            e.preventDefault();
            // Handle dietary info update
            showNotification('Dietary information updated!', 'success');
        });

        document.querySelector('.account-form').addEventListener('submit', function(e) {
            e.preventDefault();
            // Handle account update
            showNotification('Account information updated!', 'success');
        });
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
