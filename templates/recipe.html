<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ recipe.name }} - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex items-center space-x-6">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Find Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('browse_recipes') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Browse All</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Recipe Header -->
        <section class="py-12 lg:py-16 bg-organic-cream">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl lg:text-5xl font-serif font-bold text-rich-green mb-6">{{ recipe.name }}</h1>
                <div class="flex justify-center space-x-8 text-center">
                    <div>
                        <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.prep_time }}</div>
                        <div class="text-sm text-soft-gray font-medium">⏱️ Minutes</div>
                    </div>
                    <div>
                        <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.servings }}</div>
                        <div class="text-sm text-soft-gray font-medium">👥 Servings</div>
                    </div>
                    <div>
                        <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.calories }}</div>
                        <div class="text-sm text-soft-gray font-medium">🔥 Calories</div>
                    </div>
            </div>
        </div>

        <!-- Recipe Content -->
        <section class="py-12 bg-organic-beige">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid lg:grid-cols-2 gap-12">

                    <!-- Ingredients Section -->
                    <div class="bg-white rounded-3xl p-8 shadow-lg">
                        <h2 class="text-2xl font-serif font-bold text-rich-green mb-6 flex items-center">
                            <span class="text-3xl mr-3">🥘</span>
                            Ingredients
                        </h2>

                        {% if recipe.ingredients is mapping %}
                            {% for category, items in recipe.ingredients.items() %}
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-rich-green mb-3 capitalize border-b border-rich-green/20 pb-2">
                                    {{ category.replace('_', ' ').title() }}
                                </h3>
                                <ul class="space-y-2">
                                    {% for ingredient in items %}
                                    <li class="flex items-start">
                                        <span class="text-accent-green mr-2 mt-1">•</span>
                                        <span class="text-soft-gray">{{ ingredient }}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endfor %}
                        {% else %}
                            <ul class="space-y-3">
                                {% for ingredient in recipe.ingredients %}
                                <li class="flex items-start">
                                    <span class="text-accent-green mr-3 mt-1 text-lg">•</span>
                                    <span class="text-soft-gray">{{ ingredient }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        {% endif %}

                        <div class="mt-8 bg-light-sage rounded-2xl p-6">
                            <h3 class="text-lg font-semibold text-rich-green mb-3 flex items-center">
                                <span class="text-xl mr-2">💡</span>
                                Ingredient Tips
                            </h3>
                            <p class="text-soft-gray leading-relaxed">
                                Fresh ingredients make all the difference! Try to use organic produce when possible for the best flavor and nutrition. Prep all ingredients before you start cooking for a smoother experience.
                            </p>
                        </div>
                    </div>

                    <!-- Instructions Section -->
                    <div class="bg-white rounded-3xl p-8 shadow-lg">
                        <h2 class="text-2xl font-serif font-bold text-rich-green mb-6 flex items-center">
                            <span class="text-3xl mr-3">👨‍🍳</span>
                            Instructions
                        </h2>

                        {% if recipe.instructions is iterable and recipe.instructions is not string %}
                            <div class="space-y-4">
                                {% for step in recipe.instructions %}
                                <div class="flex items-start">
                                    <div class="bg-rich-green text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0 mr-4 mt-1">
                                        {{ loop.index }}
                                    </div>
                                    <p class="text-soft-gray leading-relaxed">{{ step }}</p>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-soft-gray leading-relaxed">
                                {{ recipe.instructions }}
                            </div>
                        {% endif %}

                        {% if recipe.tips %}
                        <div class="mt-8 bg-organic-cream rounded-2xl p-6">
                            <h3 class="text-lg font-semibold text-rich-green mb-3 flex items-center">
                                <span class="text-xl mr-2">💡</span>
                                Chef's Tips
                            </h3>
                            <p class="text-soft-gray leading-relaxed">{{ recipe.tips }}</p>
                        </div>
                        {% endif %}

                        <div class="mt-8 bg-light-sage rounded-2xl p-6">
                            <h3 class="text-lg font-semibold text-rich-green mb-3 flex items-center">
                                <span class="text-xl mr-2">🔥</span>
                                Cooking Tips
                            </h3>
                            <ul class="space-y-2">
                                <li class="flex items-start">
                                    <span class="text-accent-green mr-2 mt-1">•</span>
                                    <span class="text-soft-gray">Prep all ingredients before you start cooking</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-accent-green mr-2 mt-1">•</span>
                                    <span class="text-soft-gray">Taste and adjust seasoning as you go</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-accent-green mr-2 mt-1">•</span>
                                    <span class="text-soft-gray">Don't overcrowd the pan when cooking</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Nutrition Section -->
        <section class="py-12 bg-white">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-organic-cream rounded-3xl p-8">
                    <h3 class="text-2xl font-serif font-bold text-rich-green mb-6 flex items-center">
                        <span class="text-3xl mr-3">📊</span>
                        Nutrition Facts
                    </h3>
                    <p class="text-soft-gray mb-6">Per serving • Approximate values</p>

                    <div id="nutrition-info" class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="bg-white rounded-2xl p-6 shadow-sm">
                                <div class="text-3xl mb-2">🔥</div>
                                <div class="text-sm text-soft-gray mb-1">Calories</div>
                                <div class="text-2xl font-bold text-rich-green">{{ recipe.calories }}</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-white rounded-2xl p-6 shadow-sm">
                                <div class="text-3xl mb-2">🥩</div>
                                <div class="text-sm text-soft-gray mb-1">Protein</div>
                                <div class="text-2xl font-bold text-rich-green">--<span class="text-sm">g</span></div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-white rounded-2xl p-6 shadow-sm">
                                <div class="text-3xl mb-2">🍞</div>
                                <div class="text-sm text-soft-gray mb-1">Carbs</div>
                                <div class="text-2xl font-bold text-rich-green">--<span class="text-sm">g</span></div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-white rounded-2xl p-6 shadow-sm">
                                <div class="text-3xl mb-2">🧈</div>
                                <div class="text-sm text-soft-gray mb-1">Fat</div>
                                <div class="text-2xl font-bold text-rich-green">--<span class="text-sm">g</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Action Buttons -->
        <section class="py-12 bg-organic-beige">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="addToMealPlan({{ recipe.id }})" class="bg-rich-green text-white px-8 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors flex items-center justify-center">
                        <span class="mr-2">➕</span>
                        Add to Meal Plan
                    </button>
                    <button onclick="window.print()" class="border-2 border-rich-green text-rich-green px-8 py-3 rounded-full font-semibold hover:bg-rich-green hover:text-white transition-colors flex items-center justify-center">
                        <span class="mr-2">🖨️</span>
                        Print Recipe
                    </button>
                    <button onclick="shareRecipe()" class="border-2 border-rich-green text-rich-green px-8 py-3 rounded-full font-semibold hover:bg-rich-green hover:text-white transition-colors flex items-center justify-center">
                        <span class="mr-2">📤</span>
                        Share
                    </button>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors px-8 py-3 font-medium text-center">
                        ⬅️ Back to Recipes
                    </a>
                </div>
            </div>
        </section>

        <div class="recipe-suggestions">
            <h2>🍽️ You Might Also Like</h2>
            <p>Discover more delicious recipes that pair well with this dish!</p>
            <a href="{{ url_for('index') }}" class="btn btn-primary">Browse More Recipes</a>
        </div>
    </main>

    <!-- Add to Meal Plan Modal -->
    <div id="meal-plan-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>🍽️ Add to Meal Plan</h3>
            <form action="{{ url_for('add_to_meal_plan') }}" method="POST">
                <input type="hidden" id="modal-recipe-id" name="recipe_id" value="{{ recipe.id }}">
                <div class="form-group">
                    <label for="date">📅 Date:</label>
                    <input type="date" id="date" name="date" required>
                </div>
                <div class="form-group">
                    <label for="meal_type">🍽️ Meal Type:</label>
                    <select id="meal_type" name="meal_type" required>
                        <option value="">Select meal type...</option>
                        <option value="breakfast">🌅 Breakfast</option>
                        <option value="lunch">☀️ Lunch</option>
                        <option value="dinner">🌙 Dinner</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">✨ Add to Plan</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Load nutrition information
        fetch(`/nutrition-info/{{ recipe.id }}`)
            .then(response => response.json())
            .then(data => {
                const nutritionItems = document.querySelectorAll('.nutrition-item .nutrition-value');
                if (nutritionItems.length >= 4) {
                    nutritionItems[0].innerHTML = Math.round(data.calories);
                    nutritionItems[1].innerHTML = `${Math.round(data.protein)}<span class="nutrition-unit">g</span>`;
                    nutritionItems[2].innerHTML = `${Math.round(data.carbs)}<span class="nutrition-unit">g</span>`;
                    nutritionItems[3].innerHTML = `${Math.round(data.fat)}<span class="nutrition-unit">g</span>`;
                }
            })
            .catch(error => console.error('Error loading nutrition info:', error));

        function printRecipe() {
            window.print();
        }

        function shareRecipe() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ recipe.name }} - MealMind',
                    text: 'Check out this delicious recipe!',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('Recipe link copied to clipboard!', 'success');
                });
            }
        }
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
