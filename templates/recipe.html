<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ recipe.name }} - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex items-center space-x-6">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Find Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('browse_recipes') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Browse All</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Recipe Header -->
        <section class="py-12 lg:py-16 bg-organic-cream">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl lg:text-5xl font-serif font-bold text-rich-green mb-6">{{ recipe.name }}</h1>
                <div class="flex justify-center space-x-8 text-center">
                    <div>
                        <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.prep_time }}</div>
                        <div class="text-sm text-soft-gray font-medium">⏱️ Minutes</div>
                    </div>
                    <div>
                        <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.servings }}</div>
                        <div class="text-sm text-soft-gray font-medium">👥 Servings</div>
                    </div>
                    <div>
                        <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.calories }}</div>
                        <div class="text-sm text-soft-gray font-medium">🔥 Calories</div>
                    </div>
            </div>
        </div>

        <div class="recipe-content">
            <div class="ingredients-section">
                <h2>🥘 Ingredients</h2>
                {% if recipe.ingredients is mapping %}
                    {% for category, items in recipe.ingredients.items() %}
                    <div class="ingredient-category">
                        <h3 class="category-title">{{ category.title() }}</h3>
                        <ul class="ingredients-list">
                            {% for ingredient in items %}
                            <li>{{ ingredient }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endfor %}
                {% else %}
                    <ul class="ingredients-list">
                        {% for ingredient in recipe.ingredients %}
                        <li>{{ ingredient }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}

                <div class="ingredient-tips">
                    <h3>💡 Tips</h3>
                    <p>Fresh ingredients make all the difference! Try to use organic produce when possible for the best flavor and nutrition.</p>
                </div>
            </div>

            <div class="instructions-section">
                <h2>👨‍🍳 Instructions</h2>
                {% if recipe.instructions is iterable and recipe.instructions is not string %}
                    <ol class="instructions-list">
                        {% for step in recipe.instructions %}
                        <li class="instruction-step">
                            <span class="step-number">{{ loop.index }}</span>
                            <span class="step-text">{{ step }}</span>
                        </li>
                        {% endfor %}
                    </ol>
                {% else %}
                    <div class="instructions">
                        {{ recipe.instructions }}
                    </div>
                {% endif %}

                {% if recipe.tips %}
                <div class="cooking-tips-enhanced">
                    <h3>💡 Chef's Tips</h3>
                    <p>{{ recipe.tips }}</p>
                </div>
                {% endif %}

                <div class="cooking-tips">
                    <h3>🔥 Cooking Tips</h3>
                    <ul>
                        <li>Prep all ingredients before you start cooking</li>
                        <li>Taste and adjust seasoning as you go</li>
                        <li>Don't overcrowd the pan when cooking</li>
                    </ul>
                </div>
            </div>

            <div class="nutrition-section">
                <div class="nutrition-box">
                    <div class="nutrition-header">
                        <h3>📊 Nutrition Facts</h3>
                        <p class="nutrition-subtitle">Per serving • Approximate values</p>
                    </div>
                    <div id="nutrition-info" class="nutrition-grid">
                        <div class="nutrition-item">
                            <span class="nutrition-icon">🔥</span>
                            <span class="nutrition-label">Calories</span>
                            <span class="nutrition-value">{{ recipe.calories }}</span>
                        </div>
                        <div class="nutrition-item">
                            <span class="nutrition-icon">🥩</span>
                            <span class="nutrition-label">Protein</span>
                            <span class="nutrition-value">--<span class="nutrition-unit">g</span></span>
                        </div>
                        <div class="nutrition-item">
                            <span class="nutrition-icon">🍞</span>
                            <span class="nutrition-label">Carbs</span>
                            <span class="nutrition-value">--<span class="nutrition-unit">g</span></span>
                        </div>
                        <div class="nutrition-item">
                            <span class="nutrition-icon">🧈</span>
                            <span class="nutrition-label">Fat</span>
                            <span class="nutrition-value">--<span class="nutrition-unit">g</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="recipe-actions">
            <button onclick="addToMealPlan({{ recipe.id }})" class="btn btn-primary">➕ Add to Meal Plan</button>
            <button onclick="printRecipe()" class="btn btn-secondary">🖨️ Print Recipe</button>
            <button onclick="shareRecipe()" class="btn btn-secondary">📤 Share</button>
            <a href="{{ url_for('index') }}" class="btn btn-secondary">⬅️ Back to Recipes</a>
        </div>

        <div class="recipe-suggestions">
            <h2>🍽️ You Might Also Like</h2>
            <p>Discover more delicious recipes that pair well with this dish!</p>
            <a href="{{ url_for('index') }}" class="btn btn-primary">Browse More Recipes</a>
        </div>
    </main>

    <!-- Add to Meal Plan Modal -->
    <div id="meal-plan-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>🍽️ Add to Meal Plan</h3>
            <form action="{{ url_for('add_to_meal_plan') }}" method="POST">
                <input type="hidden" id="modal-recipe-id" name="recipe_id" value="{{ recipe.id }}">
                <div class="form-group">
                    <label for="date">📅 Date:</label>
                    <input type="date" id="date" name="date" required>
                </div>
                <div class="form-group">
                    <label for="meal_type">🍽️ Meal Type:</label>
                    <select id="meal_type" name="meal_type" required>
                        <option value="">Select meal type...</option>
                        <option value="breakfast">🌅 Breakfast</option>
                        <option value="lunch">☀️ Lunch</option>
                        <option value="dinner">🌙 Dinner</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">✨ Add to Plan</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Load nutrition information
        fetch(`/nutrition-info/{{ recipe.id }}`)
            .then(response => response.json())
            .then(data => {
                const nutritionItems = document.querySelectorAll('.nutrition-item .nutrition-value');
                if (nutritionItems.length >= 4) {
                    nutritionItems[0].innerHTML = Math.round(data.calories);
                    nutritionItems[1].innerHTML = `${Math.round(data.protein)}<span class="nutrition-unit">g</span>`;
                    nutritionItems[2].innerHTML = `${Math.round(data.carbs)}<span class="nutrition-unit">g</span>`;
                    nutritionItems[3].innerHTML = `${Math.round(data.fat)}<span class="nutrition-unit">g</span>`;
                }
            })
            .catch(error => console.error('Error loading nutrition info:', error));

        function printRecipe() {
            window.print();
        }

        function shareRecipe() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ recipe.name }} - MealMind',
                    text: 'Check out this delicious recipe!',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('Recipe link copied to clipboard!', 'success');
                });
            }
        }
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
