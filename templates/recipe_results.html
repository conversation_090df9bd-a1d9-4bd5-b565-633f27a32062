<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe Results - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex items-center space-x-6">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Find Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('browse_recipes') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Browse All</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Results Header -->
        <section class="py-12 lg:py-16 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl lg:text-5xl font-serif font-bold text-rich-green mb-6">🎯 Recipe Results</h1>
                <div class="bg-white rounded-2xl p-6 max-w-4xl mx-auto border border-rich-green/10">
                    <p class="text-lg text-soft-gray mb-2">
                        <strong class="text-rich-green">Your ingredients:</strong>
                        <span class="text-rich-green">{{ user_ingredients|join(', ') }}</span>
                    </p>
                    <p class="text-xl font-semibold text-rich-green">
                        Found {{ total_found }} matching recipes
                    </p>
                </div>
            </div>
        </section>

        <!-- Recipe Results -->
        {% if recipes %}
        <section class="py-12 bg-organic-beige">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-2xl font-serif font-bold text-rich-green">Recipe Matches</h2>
                    <a href="{{ url_for('index') }}" class="border-2 border-rich-green text-rich-green px-6 py-2 rounded-full font-medium hover:bg-rich-green hover:text-organic-beige transition-colors">
                        🔄 New Search
                    </a>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for recipe in recipes %}
                    <div class="bg-white rounded-2xl p-6 border border-rich-green/10 hover:border-rich-green/20 transition-colors recipe-card-organic">
                        <!-- Match Percentage -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-2">
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ recipe.match_percentage }}%</div>
                                <div class="text-sm text-soft-gray">Match</div>
                            </div>
                            <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-rich-green rounded-full transition-all duration-300" style="width: {{ recipe.match_percentage }}%"></div>
                            </div>
                        </div>

                        <!-- Recipe Info -->
                        <div class="mb-4">
                            <h3 class="text-lg font-serif font-semibold text-rich-green mb-2">{{ recipe.name }}</h3>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="ingredient-tag">{{ recipe.cuisine }}</span>
                                <span class="ingredient-tag">{{ recipe.diet_type }}</span>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="grid grid-cols-2 gap-2 text-sm text-soft-gray mb-4">
                            <div class="flex items-center">
                                <span class="mr-1">⏱️</span>
                                <span>{{ recipe.prep_time }} min</span>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1">👥</span>
                                <span>{{ recipe.servings }} servings</span>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1">🔥</span>
                                <span>{{ recipe.calories }} cal</span>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1">📊</span>
                                <span>{{ recipe.difficulty }}</span>
                            </div>
                        </div>

                        <!-- Ingredients Match -->
                        <div class="mb-4">
                            <div class="mb-3">
                                <h4 class="text-sm font-medium text-rich-green mb-2">✅ You have ({{ recipe.matched_ingredients|length }}):</h4>
                                <div class="flex flex-wrap gap-1">
                                    {% for ingredient in recipe.matched_ingredients[:3] %}
                                    <span class="text-xs bg-light-sage text-rich-green px-2 py-1 rounded-full">{{ ingredient }}</span>
                                    {% endfor %}
                                    {% if recipe.matched_ingredients|length > 3 %}
                                    <span class="text-xs text-soft-gray">+{{ recipe.matched_ingredients|length - 3 }} more</span>
                                    {% endif %}
                                </div>
                            </div>

                            {% if recipe.missing_ingredients %}
                            <div>
                                <h4 class="text-sm font-medium text-soft-gray mb-2">🛒 You need ({{ recipe.missing_ingredients|length }}):</h4>
                                <div class="flex flex-wrap gap-1">
                                    {% for ingredient in recipe.missing_ingredients[:3] %}
                                    <span class="text-xs bg-red-50 text-red-600 px-2 py-1 rounded-full">{{ ingredient }}</span>
                                    {% endfor %}
                                    {% if recipe.missing_ingredients|length > 3 %}
                                    <span class="text-xs text-soft-gray">+{{ recipe.missing_ingredients|length - 3 }} more</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Actions -->
                        <div class="flex space-x-2">
                            <a href="{{ url_for('recipe_detail', recipe_id=recipe.id) }}"
                               class="flex-1 bg-rich-green text-organic-beige py-2 px-4 rounded-lg text-sm font-medium hover:bg-soft-green transition-colors text-center">
                                👁️ View Recipe
                            </a>
                            <button onclick="addToMealPlan({{ recipe.id }})"
                                    class="flex-1 border border-rich-green text-rich-green py-2 px-4 rounded-lg text-sm font-medium hover:bg-rich-green hover:text-organic-beige transition-colors">
                                ➕ Add to Plan
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        {% else %}
        <section class="py-12 bg-organic-beige">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="bg-white rounded-2xl p-12 border border-rich-green/10">
                    <div class="text-6xl mb-4">😔</div>
            <h2>No matching recipes found</h2>
            <p>Try adjusting your ingredients or filters, or browse our full recipe collection.</p>
            <div class="no-results-actions">
                <a href="{{ url_for('index') }}" class="btn btn-primary">🔄 Try Again</a>
                <a href="{{ url_for('index') }}#browse" class="btn btn-secondary">📚 Browse All Recipes</a>
            </div>
        </div>
        {% endif %}

        <div class="tips-section">
            <h2>💡 Recipe Tips</h2>
            <div class="tips-grid">
                <div class="tip-card">
                    <div class="tip-icon">🛒</div>
                    <h3>Missing Ingredients?</h3>
                    <p>Don't worry! Many ingredients can be substituted. Check the recipe details for suggestions.</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">⭐</div>
                    <h3>High Match Recipes</h3>
                    <p>Recipes with 70%+ match are perfect for cooking right now with minimal shopping.</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">🎯</div>
                    <h3>Partial Matches</h3>
                    <p>Even 50% matches can work great - you might discover new favorite ingredients!</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Add to Meal Plan Modal -->
    <div id="meal-plan-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>🍽️ Add to Meal Plan</h3>
            <form action="{{ url_for('add_to_meal_plan') }}" method="POST">
                <input type="hidden" id="modal-recipe-id" name="recipe_id">
                <div class="form-group">
                    <label for="date">📅 Date:</label>
                    <input type="date" id="date" name="date" required>
                </div>
                <div class="form-group">
                    <label for="meal_type">🍽️ Meal Type:</label>
                    <select id="meal_type" name="meal_type" required>
                        <option value="">Select meal type...</option>
                        <option value="breakfast">🌅 Breakfast</option>
                        <option value="lunch">☀️ Lunch</option>
                        <option value="dinner">🌙 Dinner</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">✨ Add to Plan</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
