<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Recipes - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="py-16 lg:py-20 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl lg:text-6xl font-serif font-bold text-rich-green mb-6">🧠 Smart Recipes</h1>
                <p class="text-xl lg:text-2xl text-soft-gray mb-8 max-w-3xl mx-auto">
                    Let AI transform your ingredients into amazing recipes. Upload a photo or type what you have!
                </p>

                <!-- Visual Ingredient Flow -->
                <div class="max-w-4xl mx-auto">
                    <div class="grid md:grid-cols-3 gap-8 items-center">
                        <div class="text-center">
                            <div class="text-6xl mb-4">📸</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Upload Photo</h3>
                            <p class="text-soft-gray">AI identifies ingredients</p>
                        </div>

                        <div class="text-center">
                            <div class="text-6xl mb-4">🤖</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Smart Analysis</h3>
                            <p class="text-soft-gray">AI creates custom recipes</p>
                        </div>

                        <div class="text-center">
                            <div class="text-6xl mb-4">🍽️</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Custom Recipes</h3>
                            <p class="text-soft-gray">Using only your ingredients</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Ingredient Finder Section -->
        <section id="input-methods" class="py-16 lg:py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-4">Find Your Perfect Recipe</h2>
                    <p class="text-lg text-soft-gray max-w-2xl mx-auto">Choose your preferred method to discover amazing recipes tailored to your ingredients</p>
                </div>

                <form action="{{ url_for('find_recipes') }}" method="POST" enctype="multipart/form-data" class="max-w-6xl mx-auto">
                    <div class="grid lg:grid-cols-2 gap-8">
                        <!-- Photo Upload Method -->
                        <div class="bg-organic-beige rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-colors">
                            <div class="text-center mb-6">
                                <div class="text-4xl mb-3">📸</div>
                                <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Smart Photo Recognition</h3>
                                <p class="text-soft-gray">Upload a photo and let AI identify your ingredients</p>
                            </div>

                            <div class="image-upload-zone" id="image-upload-area">
                                <input type="file" id="ingredients_image" name="ingredients_image" accept="image/*" style="display: none;">
                                <div class="upload-placeholder border-2 border-dashed border-rich-green/30 rounded-xl p-8 text-center hover:border-rich-green/50 transition-colors cursor-pointer bg-white" id="upload-placeholder">
                                    <div class="text-4xl mb-4 text-rich-green/60">📷</div>
                                    <div class="mb-4">
                                        <div class="text-lg font-medium text-rich-green mb-1">Drop your photo here</div>
                                        <div class="text-soft-gray">or click to browse</div>
                                    </div>
                                    <div class="text-sm text-soft-gray">
                                        Supports: JPG, PNG, GIF, WebP (max 16MB)
                                    </div>
                                </div>
                                <div id="image-preview" class="hidden mt-4">
                                    <img id="preview-img" class="w-full h-48 object-cover rounded-xl border border-rich-green/20">
                                    <button type="button" id="remove-image" class="mt-2 text-sm text-red-500 hover:text-red-700">Remove Image</button>
                                </div>
                            </div>
                        </div>

                        <!-- Text Input Method -->
                        <div class="bg-organic-beige rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-colors">
                            <div class="text-center mb-6">
                                <div class="text-4xl mb-3">✍️</div>
                                <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Type Your Ingredients</h3>
                                <p class="text-soft-gray">Type your ingredients for precise control</p>
                            </div>

                            <div class="text-input-zone">
                                <textarea
                                    id="ingredients_text"
                                    name="ingredients_text"
                                    placeholder="Enter your ingredients here...

Examples:
• chicken breast, tomatoes, onion, garlic
• pasta, cheese, herbs, olive oil
• rice, vegetables, soy sauce"
                                    rows="8"
                                    class="w-full p-4 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none resize-none text-rich-green placeholder-soft-gray bg-white"
                                    oninput="identifyIngredientsFromText()"
                                ></textarea>
                                <div class="mt-3">
                                    <div class="text-sm text-soft-gray mb-2">
                                        💡 Tip: Our AI will automatically identify ingredients as you type
                                    </div>
                                    <div id="identified-ingredients-container" class="hidden">
                                        <div class="text-sm font-medium text-rich-green mb-2">🤖 AI Identified Ingredients:</div>
                                        <div id="identified-ingredients-tags" class="flex flex-wrap gap-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="text-center mt-8">
                        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <button type="submit" class="bg-rich-green text-organic-beige px-12 py-4 rounded-full text-lg font-semibold hover:bg-soft-green transition-colors shadow-lg hover:shadow-xl">
                                🔍 Find Matching Recipes
                            </button>
                            <div class="text-soft-gray font-medium">OR</div>
                            <button type="submit" formaction="{{ url_for('generate_recipe') }}" class="bg-accent-green text-white px-12 py-4 rounded-full text-lg font-semibold hover:bg-soft-green transition-colors shadow-lg hover:shadow-xl">
                                ✨ Generate Custom Recipe
                            </button>
                        </div>
                        <div class="mt-4 text-sm text-soft-gray max-w-2xl mx-auto">
                            <strong>Find Matching:</strong> Discover existing recipes that use some of your ingredients<br>
                            <strong>Generate Custom:</strong> Create a unique recipe using ONLY your ingredients
                        </div>
                    </div>
                </form>
            </div>
        </section>
    </main>
    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>



    <!-- JavaScript for functionality -->
    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- ML Ingredient Identification JavaScript -->
    <script>
        let identificationTimeout;

        function identifyIngredientsFromText() {
            // Clear previous timeout
            clearTimeout(identificationTimeout);

            // Set a new timeout to avoid too many API calls
            identificationTimeout = setTimeout(() => {
                const text = document.getElementById('ingredients_text').value.trim();

                if (text.length < 3) {
                    document.getElementById('identified-ingredients-container').classList.add('hidden');
                    return;
                }

                // Call ML API to identify ingredients
                fetch('/api/identify-ingredients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.ingredients.length > 0) {
                        displayIdentifiedIngredients(data.ingredients);
                    } else {
                        document.getElementById('identified-ingredients-container').classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error identifying ingredients:', error);
                });
            }, 500); // Wait 500ms after user stops typing
        }

        function displayIdentifiedIngredients(ingredients) {
            const container = document.getElementById('identified-ingredients-container');
            const tagsContainer = document.getElementById('identified-ingredients-tags');

            // Clear previous tags
            tagsContainer.innerHTML = '';

            // Add new tags
            ingredients.forEach(ingredient => {
                const tag = document.createElement('span');
                tag.className = 'ingredient-tag';
                tag.textContent = `${ingredient.name} (${ingredient.category})`;

                // Add confidence indicator
                if (ingredient.confidence > 0.8) {
                    tag.style.backgroundColor = '#E8F5E8';
                    tag.style.borderColor = '#1A5E2A';
                } else {
                    tag.style.backgroundColor = '#FEF3C7';
                    tag.style.borderColor = '#D97706';
                }

                tagsContainer.appendChild(tag);
            });

            // Show container
            container.classList.remove('hidden');
        }
    </script>

    <!-- Mobile Menu JavaScript -->
    <script>
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html>
